"""
客户邀请数据处理模块 - PostgreSQL版本
提供客户邀请相关的数据库操作功能
主要功能：
1. 创建客户邀请记录
2. 生成邀请码和邀请链接
3. 查询邀请列表和详情
4. 更新邀请状态
5. 处理邀请接受逻辑
6. 支持每日邀请次数限制
"""

import os
import secrets
import string
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any

from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 日志 import 错误日志器, 数据库日志器
from 数据.Postgre_异步数据库函数 import Postgre_异步用户是否已注册_电话
from 状态 import 客户邀请

# 邀请状态常量
CUSTOMER_INVITATION_STATUS = {
    "PENDING": "待处理",
    "REGISTERED": "已注册", 
    "EXPIRED": "已过期",
    "REJECTED": "已拒绝"
}

def Postgre_生成客户邀请码(长度: int = 32) -> str:
    """生成客户邀请码"""
    字符集 = string.ascii_letters + string.digits
    return ''.join(secrets.choice(字符集) for _ in range(长度))

async def Postgre_异步创建客户邀请(
    邀请人ID: int,
    被邀请人手机号: str,
    邀请消息: Optional[str] = None,
    有效期天数: int = 30
) -> Dict[str, Any]:
    """
    创建客户邀请记录
    """
    try:
        数据库日志器.info(f"开始创建客户邀请：邀请人ID={邀请人ID}, 被邀请人手机号={被邀请人手机号}")
        
        # 检查被邀请人是否已注册
        用户已注册 = await Postgre_异步用户是否已注册_电话(被邀请人手机号)
        if 用户已注册:
            return {
                "status": 客户邀请.手机号已注册,
                "message": f"手机号 {被邀请人手机号} 已注册，无需邀请"
            }
        
        # 检查是否存在相同手机号的待处理邀请
        检查SQL = """
        SELECT id, 邀请状态, 过期时间 FROM 用户客户邀请记录表
        WHERE 邀请人ID = $1 AND 被邀请人手机号 = $2 AND 邀请状态 = $3
        """
        现有邀请 = await 异步连接池实例.执行查询(检查SQL, (邀请人ID, 被邀请人手机号, CUSTOMER_INVITATION_STATUS["PENDING"]))
        
        if 现有邀请:
            # 更新现有邀请
            邀请码 = Postgre_生成客户邀请码()
            过期时间 = datetime.now() + timedelta(days=有效期天数)
            
            更新SQL = """
            UPDATE 用户客户邀请记录表 SET
                邀请码 = $1, 邀请消息 = $2, 过期时间 = $3, 更新时间 = $4
            WHERE id = $5
            """
            await 异步连接池实例.执行更新(更新SQL, (
                邀请码, 邀请消息, 过期时间, datetime.now(), 现有邀请[0]['id']
            ))
            
            邀请ID = 现有邀请[0]['id']
            消息 = "已更新现有邀请记录"
        else:
            # 创建新邀请
            邀请码 = Postgre_生成客户邀请码()
            过期时间 = datetime.now() + timedelta(days=有效期天数)
            
            插入SQL = """
            INSERT INTO 用户客户邀请记录表(
                邀请人ID, 被邀请人手机号, 邀请码, 邀请状态, 
                邀请消息, 邀请时间, 过期时间, 邀请来源, 创建时间
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
            RETURNING id
            """
            邀请ID = await 异步连接池实例.执行插入(插入SQL, (
                邀请人ID, 被邀请人手机号, 邀请码, CUSTOMER_INVITATION_STATUS["PENDING"],
                邀请消息, datetime.now(), 过期时间, "system_settings", datetime.now()
            ))
            
            消息 = "客户邀请创建成功"
        
        # 生成邀请链接 - 直接跳转到注册页面并携带邀请码
        前端域名 = os.getenv('FRONTEND_URL', 'https://crm.limob.cn')
        邀请链接 = f"{前端域名}/register?inviteCode={邀请码}"
        
        数据库日志器.info(f"客户邀请创建成功，邀请ID: {邀请ID}")
        
        return {
            "success": True,
            "message": 消息,
            "data": {
                "邀请ID": 邀请ID,
                "邀请码": 邀请码,
                "邀请链接": 邀请链接,
                "过期时间": 过期时间.isoformat(),
                "有效期天数": 有效期天数,
                "被邀请人手机号": 被邀请人手机号
            }
        }
                
    except Exception as e:
        错误日志器.error(f"创建客户邀请失败: {e}", exc_info=True)
        return {"success": False, "message": "创建邀请失败，请稍后重试"}

async def Postgre_异步获取客户邀请列表(
    邀请人ID: int,
    页码: int = 1,
    每页数量: int = 20,
    状态筛选: Optional[str] = None
) -> Dict[str, Any]:
    """获取客户邀请列表"""
    try:
        数据库日志器.info(f"获取客户邀请列表 邀请人ID={邀请人ID}, 页码={页码}, 状态筛选={状态筛选}")
        
        # 构建查询条件
        条件列表 = ["uci.邀请人ID = $1"]
        参数列表 = [邀请人ID]
        参数索引 = 2
        
        if 状态筛选:
            条件列表.append(f"uci.邀请状态 = ${参数索引}")
            参数列表.append(状态筛选)
            参数索引 += 1
        
        条件SQL = " AND ".join(条件列表)
        
        # 获取总数
        计数SQL = f"""
        SELECT COUNT(*) as total FROM 用户客户邀请记录表 uci 
        WHERE {条件SQL}
        """
        总数结果 = await 异步连接池实例.执行查询(计数SQL, tuple(参数列表))
        总数 = 总数结果[0]['total'] if 总数结果 else 0
        
        # 获取分页数据
        查询SQL = f"""
        SELECT 
            uci.id as 邀请ID, uci.邀请人ID, uci.被邀请人手机号, uci.被邀请人ID,
            uci.邀请码, uci.邀请状态, uci.邀请消息, uci.邀请时间,
            uci.注册时间, uci.过期时间, uci.邀请来源, uci.奖励状态,
            u1.昵称 as 邀请人姓名, u1.手机号 as 邀请人手机,
            u2.昵称 as 被邀请人姓名
        FROM 用户客户邀请记录表 uci
        LEFT JOIN 用户表 u1 ON uci.邀请人ID = u1.id
        LEFT JOIN 用户表 u2 ON uci.被邀请人ID = u2.id
        WHERE {条件SQL}
        ORDER BY uci.邀请时间 DESC
        LIMIT ${参数索引} OFFSET ${参数索引 + 1}
        """
        
        偏移量 = (页码 - 1) * 每页数量
        参数列表.extend([每页数量, 偏移量])
        
        邀请记录列表 = await 异步连接池实例.执行查询(查询SQL, tuple(参数列表))
        
        # 处理邀请记录，添加邀请链接 - 统一使用线上域名
        处理后的邀请列表 = []
        前端域名 = os.getenv('FRONTEND_URL', 'https://crm.limob.cn')
        
        for 记录 in 邀请记录列表:
            邀请链接 = f"{前端域名}/register?inviteCode={记录['邀请码']}"
            
            # 处理被邀请人姓名显示逻辑
            if 记录['邀请状态'] == CUSTOMER_INVITATION_STATUS["REGISTERED"]:
                # 已注册状态，如果昵称为空则使用手机号
                被邀请人姓名 = 记录['被邀请人姓名'] or 记录['被邀请人手机号']
            else:
                # 未注册状态
                被邀请人姓名 = "未注册"
            
            邀请记录 = {
                "id": 记录['邀请ID'],
                "邀请ID": 记录['邀请ID'],
                "邀请人ID": 记录['邀请人ID'],
                "被邀请人手机号": 记录['被邀请人手机号'],
                "被邀请人ID": 记录['被邀请人ID'],
                "被邀请人姓名": 被邀请人姓名,
                "邀请人姓名": 记录['邀请人姓名'] or "未知",
                "邀请码": 记录['邀请码'],
                "邀请状态": 记录['邀请状态'],
                "邀请消息": 记录['邀请消息'],
                "邀请链接": 邀请链接,
                "邀请时间": 记录['邀请时间'].isoformat() if 记录['邀请时间'] else None,
                "注册时间": 记录['注册时间'].isoformat() if 记录['注册时间'] else None,
                "过期时间": 记录['过期时间'].isoformat() if 记录['过期时间'] else None,
                "邀请来源": 记录['邀请来源'],
                "奖励状态": 记录['奖励状态']
            }
            
            处理后的邀请列表.append(邀请记录)
        
        总页数 = (总数 + 每页数量 - 1) // 每页数量
        
        return {
            "success": True,
            "data": {
                "邀请列表": 处理后的邀请列表,
                "总数": 总数,
                "页码": 页码,
                "每页数量": 每页数量,
                "总页数": 总页数
            }
        }
                
    except Exception as e:
        错误日志器.error(f"获取客户邀请列表失败: {e}", exc_info=True)
        return {
            "success": False,
            "message": f"获取邀请列表失败：{str(e)}"
        }

async def Postgre_异步获取邀请详情(邀请码: str) -> Dict[str, Any]:
    """通过邀请码获取邀请详情"""
    try:
        数据库日志器.info(f"查找邀请码: {邀请码}")

        查询SQL = """
        SELECT
            uci.id as 邀请ID, uci.邀请人ID, uci.被邀请人手机号, uci.被邀请人ID,
            uci.邀请码, uci.邀请状态, uci.邀请消息, uci.邀请时间,
            uci.注册时间, uci.过期时间, uci.邀请来源,
            u1.昵称 as 邀请人姓名, u1.手机号 as 邀请人手机
        FROM 用户客户邀请记录表 uci
        LEFT JOIN 用户表 u1 ON uci.邀请人ID = u1.id
        WHERE uci.邀请码 = $1
        """

        邀请信息结果 = await 异步连接池实例.执行查询(查询SQL, (邀请码,))

        if not 邀请信息结果:
            return {
                "success": False,
                "status": 404,
                "message": "邀请不存在或邀请码无效"
            }

        邀请信息 = 邀请信息结果[0]

        # 检查邀请状态
        if 邀请信息['邀请状态'] == CUSTOMER_INVITATION_STATUS["EXPIRED"]:
            return {
                "success": False,
                "status": 410,
                "message": "邀请已过期"
            }

        if 邀请信息['邀请状态'] == CUSTOMER_INVITATION_STATUS["REGISTERED"]:
            return {
                "success": False,
                "status": 409,
                "message": "邀请已被使用"
            }

        # 检查是否过期
        if 邀请信息['过期时间'] and datetime.now() > 邀请信息['过期时间']:
            # 更新状态为已过期
            await Postgre_更新邀请状态(邀请信息['邀请ID'], CUSTOMER_INVITATION_STATUS["EXPIRED"])
            return {
                "success": False,
                "status": 410,
                "message": "邀请已过期"
            }

        # 检查被邀请人是否已注册
        用户已注册 = await Postgre_异步用户是否已注册_电话(邀请信息['被邀请人手机号'])
        if 用户已注册:
            # 更新邀请状态 - 需要获取用户ID
            用户查询SQL = "SELECT id FROM 用户表 WHERE 手机号 = $1"
            用户信息结果 = await 异步连接池实例.执行查询(用户查询SQL, (邀请信息['被邀请人手机号'],))
            用户ID = 用户信息结果[0]['id'] if 用户信息结果 else None

            await Postgre_更新邀请状态(邀请信息['邀请ID'], CUSTOMER_INVITATION_STATUS["REGISTERED"], 用户ID)
            return {
                "success": False,
                "status": 409,
                "message": "该手机号已注册，邀请已失效"
            }

        return {
            "success": True,
            "status": 100,
            "data": {
                "邀请ID": 邀请信息['邀请ID'],
                "邀请人ID": 邀请信息['邀请人ID'],
                "邀请人姓名": 邀请信息['邀请人姓名'],
                "被邀请人手机号": 邀请信息['被邀请人手机号'],
                "邀请消息": 邀请信息['邀请消息'],
                "邀请时间": 邀请信息['邀请时间'].isoformat() if 邀请信息['邀请时间'] else None,
                "过期时间": 邀请信息['过期时间'].isoformat() if 邀请信息['过期时间'] else None,
                "邀请状态": 邀请信息['邀请状态']
            }
        }

    except Exception as e:
        错误日志器.error(f"获取邀请详情失败: {e}", exc_info=True)
        return {
            "success": False,
            "status": 500,
            "message": f"获取邀请详情失败：{str(e)}"
        }

async def Postgre_更新邀请状态(邀请ID: int, 新状态: str, 被邀请人ID: Optional[int] = None) -> bool:
    """更新邀请状态"""
    try:
        if 新状态 == CUSTOMER_INVITATION_STATUS["REGISTERED"] and 被邀请人ID:
            更新SQL = """
            UPDATE 用户客户邀请记录表 SET
                邀请状态 = $1, 被邀请人ID = $2, 注册时间 = $3, 更新时间 = $4
            WHERE id = $5
            """
            await 异步连接池实例.执行更新(更新SQL, (新状态, 被邀请人ID, datetime.now(), datetime.now(), 邀请ID))
        else:
            更新SQL = """
            UPDATE 用户客户邀请记录表 SET
                邀请状态 = $1, 更新时间 = $2
            WHERE id = $3
            """
            await 异步连接池实例.执行更新(更新SQL, (新状态, datetime.now(), 邀请ID))

        数据库日志器.info(f"邀请状态更新成功，邀请ID: {邀请ID}, 新状态: {新状态}")
        return True

    except Exception as e:
        错误日志器.error(f"更新邀请状态失败: {e}", exc_info=True)
        return False

async def Postgre_处理用户注册邀请关联(手机号: str, 用户ID: int) -> Dict[str, Any]:
    """处理用户注册时的邀请关联逻辑"""
    try:
        数据库日志器.info(f"处理用户注册邀请关联：手机号={手机号}, 用户ID={用户ID}")

        # 查找该手机号的待处理邀请，并检查是否未过期
        查询SQL = """
        SELECT id, 邀请人ID, 邀请码, 过期时间 FROM 用户客户邀请记录表
        WHERE 被邀请人手机号 = $1 AND 邀请状态 = $2 AND 过期时间 > $3
        ORDER BY 邀请时间 DESC
        LIMIT 1
        """
        邀请记录结果 = await 异步连接池实例.执行查询(查询SQL, (手机号, CUSTOMER_INVITATION_STATUS["PENDING"], datetime.now()))

        if 邀请记录结果:
            邀请记录 = 邀请记录结果[0]
            # 更新邀请记录状态
            await Postgre_更新邀请状态(邀请记录['id'], CUSTOMER_INVITATION_STATUS["REGISTERED"], 用户ID)

            # 更新用户表的邀请人字段（代理类型表id相关逻辑已移除）
            try:
                from 数据.Postgre_异步数据库函数 import Postgre_异步更新用户邀请关联信息
                # 使用统一函数更新邀请人
                更新成功 = await Postgre_异步更新用户邀请关联信息(用户ID, 邀请记录['邀请人ID'])
                if 更新成功:
                    数据库日志器.info(f"成功更新用户ID={用户ID}的邀请关联信息")
                else:
                    数据库日志器.warning(f"更新用户ID={用户ID}的邀请关联信息失败")

            except ImportError:
                # 如果新函数不存在，使用原有逻辑
                from 数据.Postgre_异步数据库函数 import Postgre_异步更新用户邀请人
                await Postgre_异步更新用户邀请人(用户ID, 邀请记录['邀请人ID'])

            数据库日志器.info(f"用户注册邀请关联成功：用户ID={用户ID}, 邀请人ID={邀请记录['邀请人ID']}")

            return {
                "success": True,
                "message": "邀请关联成功",
                "data": {
                    "邀请人ID": 邀请记录['邀请人ID'],
                    "邀请码": 邀请记录['邀请码']
                }
            }
        else:
            return {
                "success": False,
                "message": "未找到相关邀请记录"
            }

    except Exception as e:
        错误日志器.error(f"处理用户注册邀请关联失败: {e}", exc_info=True)
        return {
            "success": False,
            "message": f"邀请关联失败：{str(e)}"
        }

async def Postgre_处理用户注册邀请码关联(手机号: str, 用户ID: int, 邀请码: str) -> Dict[str, Any]:
    """基于邀请码的用户注册邀请关联逻辑"""
    try:
        数据库日志器.info(f"处理用户注册邀请码关联：手机号={手机号}, 用户ID={用户ID}, 邀请码={邀请码}")

        # 根据邀请码查找邀请记录
        查询SQL = """
        SELECT id, 邀请人ID, 邀请码, 被邀请人手机号, 邀请状态, 过期时间 FROM 用户客户邀请记录表
        WHERE 邀请码 = $1
        """
        邀请记录结果 = await 异步连接池实例.执行查询(查询SQL, (邀请码,))

        if not 邀请记录结果:
            return {
                "success": False,
                "message": "邀请码不存在或无效"
            }

        邀请记录 = 邀请记录结果[0]

        # 检查邀请状态
        if 邀请记录['邀请状态'] != CUSTOMER_INVITATION_STATUS["PENDING"]:
            数据库日志器.warning(f"邀请码 {邀请码} 状态为 {邀请记录['邀请状态']}，不是待处理状态")
            return {
                "success": False,
                "message": f"邀请码已{邀请记录['邀请状态']}，无法使用"
            }

        # 检查邀请是否过期
        if 邀请记录['过期时间'] and datetime.now() > 邀请记录['过期时间']:
            数据库日志器.warning(f"邀请码 {邀请码} 已过期")
            return {
                "success": False,
                "message": "邀请码已过期，无法使用"
            }

        # 检查手机号是否匹配
        if 邀请记录['被邀请人手机号'] != 手机号:
            数据库日志器.warning(f"邀请码 {邀请码} 的被邀请人手机号 {邀请记录['被邀请人手机号']} 与注册手机号 {手机号} 不匹配")
            return {
                "success": False,
                "message": "邀请码与注册手机号不匹配"
            }

        # 更新邀请记录状态
        await Postgre_更新邀请状态(邀请记录['id'], CUSTOMER_INVITATION_STATUS["REGISTERED"], 用户ID)

        # 更新用户表的邀请人字段（代理类型表id相关逻辑已移除）
        try:
            from 数据.Postgre_异步数据库函数 import Postgre_异步更新用户邀请关联信息
            # 使用统一函数更新邀请人
            更新成功 = await Postgre_异步更新用户邀请关联信息(用户ID, 邀请记录['邀请人ID'])
            if 更新成功:
                数据库日志器.info(f"成功更新用户ID={用户ID}的邀请关联信息")
            else:
                数据库日志器.warning(f"更新用户ID={用户ID}的邀请关联信息失败")

        except ImportError:
            # 如果新函数不存在，使用原有逻辑
            from 数据.Postgre_异步数据库函数 import Postgre_异步更新用户邀请人
            await Postgre_异步更新用户邀请人(用户ID, 邀请记录['邀请人ID'])

        数据库日志器.info(f"用户注册邀请码关联成功：用户ID={用户ID}, 邀请人ID={邀请记录['邀请人ID']}, 邀请码={邀请码}")

        return {
            "success": True,
            "message": "邀请关联成功",
            "data": {
                "邀请人ID": 邀请记录['邀请人ID'],
                "邀请码": 邀请码
            }
        }

    except Exception as e:
        错误日志器.error(f"处理用户注册邀请码关联失败: {e}", exc_info=True)
        return {
            "success": False,
            "message": f"邀请关联失败：{str(e)}"
        }

async def Postgre_异步撤销客户邀请(邀请ID: int, 邀请人ID: int) -> Dict[str, Any]:
    """撤销客户邀请"""
    try:
        数据库日志器.info(f"撤销客户邀请：邀请ID={邀请ID}, 邀请人ID={邀请人ID}")

        # 查找邀请记录并验证权限
        查询SQL = """
        SELECT id, 邀请人ID, 邀请状态, 被邀请人手机号 FROM 用户客户邀请记录表
        WHERE id = $1 AND 邀请人ID = $2
        """
        邀请记录结果 = await 异步连接池实例.执行查询(查询SQL, (邀请ID, 邀请人ID))

        if not 邀请记录结果:
            return {
                "success": False,
                "message": "邀请记录不存在或无权限操作"
            }

        邀请记录 = 邀请记录结果[0]

        if 邀请记录['邀请状态'] != CUSTOMER_INVITATION_STATUS["PENDING"]:
            return {
                "success": False,
                "message": "只能撤销待处理状态的邀请"
            }

        # 撤销邀请，状态改为已拒绝
        更新成功 = await Postgre_更新邀请状态(邀请ID, CUSTOMER_INVITATION_STATUS["REJECTED"])

        if 更新成功:
            数据库日志器.info(f"邀请撤销成功：邀请ID={邀请ID}")
            return {
                "success": True,
                "message": f"已撤销对 {邀请记录['被邀请人手机号']} 的邀请"
            }
        else:
            return {
                "success": False,
                "message": "撤销邀请失败"
            }

    except Exception as e:
        错误日志器.error(f"撤销客户邀请失败: {e}", exc_info=True)
        return {
            "success": False,
            "message": f"撤销邀请失败：{str(e)}"
        }

async def Postgre_异步重新发送客户邀请(邀请ID: int, 邀请人ID: int) -> Dict[str, Any]:
    """重新发送客户邀请"""
    try:
        数据库日志器.info(f"重新发送客户邀请：邀请ID={邀请ID}, 邀请人ID={邀请人ID}")

        # 查找邀请记录并验证权限
        查询SQL = """
        SELECT id, 邀请人ID, 邀请状态, 被邀请人手机号, 邀请消息, 有效期天数 FROM 用户客户邀请记录表
        WHERE id = $1 AND 邀请人ID = $2
        """
        邀请记录结果 = await 异步连接池实例.执行查询(查询SQL, (邀请ID, 邀请人ID))

        if not 邀请记录结果:
            return {
                "success": False,
                "message": "邀请记录不存在或无权限操作"
            }

        邀请记录 = 邀请记录结果[0]

        if 邀请记录['邀请状态'] not in [CUSTOMER_INVITATION_STATUS["EXPIRED"], CUSTOMER_INVITATION_STATUS["REJECTED"]]:
            return {
                "success": False,
                "message": "只能重新发送已过期或已拒绝的邀请"
            }

        # 检查被邀请人是否已注册
        用户已注册 = await Postgre_异步用户是否已注册_电话(邀请记录['被邀请人手机号'])
        if 用户已注册:
            return {
                "success": False,
                "message": f"手机号 {邀请记录['被邀请人手机号']} 已注册，无需重新发送邀请"
            }

        # 生成新的邀请码和过期时间
        新邀请码 = Postgre_生成客户邀请码()
        新过期时间 = datetime.now() + timedelta(days=邀请记录.get('有效期天数', 30))

        # 更新邀请记录
        更新SQL = """
        UPDATE 用户客户邀请记录表 SET
            邀请码 = $1, 邀请状态 = $2, 过期时间 = $3,
            邀请时间 = $4, 更新时间 = $5
        WHERE id = $6
        """
        await 异步连接池实例.执行更新(更新SQL, (
            新邀请码, CUSTOMER_INVITATION_STATUS["PENDING"], 新过期时间,
            datetime.now(), datetime.now(), 邀请ID
        ))

        # 生成新的邀请链接
        前端域名 = os.getenv('FRONTEND_URL', 'https://crm.limob.cn')
        邀请链接 = f"{前端域名}/register?inviteCode={新邀请码}"

        数据库日志器.info(f"重新发送邀请成功：邀请ID={邀请ID}")
        return {
            "success": True,
            "message": f"已重新发送邀请给 {邀请记录['被邀请人手机号']}",
            "data": {
                "邀请码": 新邀请码,
                "邀请链接": 邀请链接,
                "过期时间": 新过期时间.isoformat()
            }
        }

    except Exception as e:
        错误日志器.error(f"重新发送客户邀请失败: {e}", exc_info=True)
        return {
            "success": False,
            "message": f"重新发送邀请失败：{str(e)}"
        }

async def Postgre_异步获取邀请统计(邀请人ID: int) -> Dict[str, Any]:
    """获取邀请统计信息"""
    try:
        # 获取各状态的邀请数量
        统计SQL = """
        SELECT
            邀请状态,
            COUNT(*) as 数量
        FROM 用户客户邀请记录表
        WHERE 邀请人ID = $1
        GROUP BY 邀请状态
        """
        状态统计 = await 异步连接池实例.执行查询(统计SQL, (邀请人ID,))

        # 整理统计数据
        统计数据 = {
            "总邀请数": 0,
            "待处理": 0,
            "已注册": 0,
            "已过期": 0,
            "已拒绝": 0
        }

        for 记录 in 状态统计:
            状态 = 记录['邀请状态']
            数量 = 记录['数量']
            统计数据["总邀请数"] += 数量

            if 状态 == CUSTOMER_INVITATION_STATUS["PENDING"]:
                统计数据["待处理"] = 数量
            elif 状态 == CUSTOMER_INVITATION_STATUS["REGISTERED"]:
                统计数据["已注册"] = 数量
            elif 状态 == CUSTOMER_INVITATION_STATUS["EXPIRED"]:
                统计数据["已过期"] = 数量
            elif 状态 == CUSTOMER_INVITATION_STATUS["REJECTED"]:
                统计数据["已拒绝"] = 数量

        return {
            "success": True,
            "data": 统计数据
        }

    except Exception as e:
        错误日志器.error(f"获取邀请统计失败: {e}", exc_info=True)
        return {
            "success": False,
            "message": f"获取统计信息失败：{str(e)}"
        }
