"""
PostgreSQL线索数据操作
基于asyncpg实现的线索数据访问层

特性：
1. 使用PostgreSQL JSONB类型存储线索信息
2. 支持UPSERT操作（ON CONFLICT DO UPDATE）
3. 使用$1, $2参数占位符，防止SQL注入
4. 充分利用PostgreSQL的JSON操作符
5. 高效的分页查询和条件筛选
"""

import json
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple

from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 日志 import 数据库日志器, 错误日志器


async def Postgre_获取或创建联系方式并返回完整数据(
    内容: str, 
    类型: Optional[str], 
    记录来源: Optional[str]
) -> Optional[Dict[str, Any]]:
    """
    根据联系方式内容和类型查询 kol.联系方式表。
    如果找到匹配记录，则返回其完整数据。
    如果未找到，则创建新记录，并返回新记录的完整数据。
    使用PostgreSQL的UPSERT机制处理并发插入的唯一键冲突。
    """
    当前时间 = datetime.now()

    # PostgreSQL UPSERT SQL 语句
    # 当 (联系方式, 类型) 发生唯一键冲突时:
    # 1. 更新 '更新时间' 为当前时间
    # 2. 使用 RETURNING 子句返回记录ID
    创建_sql_upsert = """
    INSERT INTO kol.联系方式表 (联系方式, 类型, 来源, 创建时间, 更新时间)
    VALUES ($1, $2, $3, $4, $5)
    ON CONFLICT (联系方式, 类型) 
    DO UPDATE SET
        更新时间 = EXCLUDED.更新时间,
        来源 = COALESCE(EXCLUDED.来源, kol.联系方式表.来源)
    RETURNING id
    """
    插入参数 = (内容, 类型, 记录来源, 当前时间, 当前时间)

    数据库日志器.debug(f"[获取或创建联系方式并返回完整数据_UPSERT] SQL: {创建_sql_upsert} 参数: {插入参数}")

    try:
        # 执行UPSERT操作并获取ID
        结果 = await 异步连接池实例.执行查询(创建_sql_upsert, 插入参数)
        
        if not 结果:
            错误日志器.error(f"[获取或创建联系方式并返回完整数据_UPSERT] UPSERT操作未返回结果")
            return None
            
        记录ID = 结果[0]["id"]
        数据库日志器.debug(f"[获取或创建联系方式并返回完整数据_UPSERT] 成功获取/创建记录，ID: {记录ID}")

        # 查询完整记录数据
        查询_sql = """
        SELECT id, 联系方式, 类型, 来源, 创建时间, 更新时间
        FROM kol.联系方式表
        WHERE id = $1
        """
        
        完整数据 = await 异步连接池实例.执行查询(查询_sql, (记录ID,))
        
        if 完整数据:
            数据库日志器.debug(f"[获取或创建联系方式并返回完整数据_UPSERT] 成功返回完整数据: {完整数据[0]}")
            return 完整数据[0]
        else:
            错误日志器.error(f"[获取或创建联系方式并返回完整数据_UPSERT] 无法查询到刚创建/更新的记录，ID: {记录ID}")
            return None

    except Exception as e:
        错误日志器.error(f"[获取或创建联系方式并返回完整数据_UPSERT] 操作失败: {str(e)}", exc_info=True)
        return None


async def Postgre_创建线索记录(
    联系方式id: int,
    额外信息: Optional[Dict[str, Any]] = None,
    记录来源: Optional[str] = None
) -> Optional[int]:
    """
    创建新的线索记录

    Args:
        联系方式id: 联系方式表的ID
        额外信息: 额外的线索信息（存储为JSONB）
        记录来源: 记录来源

    Returns:
        新创建的线索ID或None
    """
    try:
        当前时间 = datetime.now()
        
        # 将额外信息转换为JSONB
        信息_jsonb = json.dumps(额外信息, ensure_ascii=False) if 额外信息 else None
        
        插入_sql = """
        INSERT INTO kol.线索表 (联系方式id, 信息, 来源, 创建时间, 更新时间)
        VALUES ($1, $2::jsonb, $3, $4, $5)
        RETURNING id
        """
        
        结果 = await 异步连接池实例.执行查询(
            插入_sql, 
            (联系方式id, 信息_jsonb, 记录来源, 当前时间, 当前时间)
        )
        
        if 结果:
            线索ID = 结果[0]["id"]
            数据库日志器.info(f"成功创建线索记录，ID: {线索ID}")
            return 线索ID
        else:
            错误日志器.error("创建线索记录失败，未返回ID")
            return None
            
    except Exception as e:
        错误日志器.error(f"创建线索记录异常: {str(e)}", exc_info=True)
        return None


async def Postgre_更新线索信息(
    线索id: int,
    新信息: Dict[str, Any],
    更新来源: Optional[str] = None
) -> bool:
    """
    更新线索的额外信息

    Args:
        线索id: 线索ID
        新信息: 新的信息字典
        更新来源: 更新来源

    Returns:
        是否更新成功
    """
    try:
        当前时间 = datetime.now()
        信息_jsonb = json.dumps(新信息, ensure_ascii=False)
        
        更新_sql = """
        UPDATE kol.线索表 
        SET 信息 = $1::jsonb, 更新时间 = $2
        WHERE id = $3
        """
        
        if 更新来源:
            更新_sql = """
            UPDATE kol.线索表 
            SET 信息 = $1::jsonb, 更新时间 = $2, 来源 = $4
            WHERE id = $3
            """
            影响行数 = await 异步连接池实例.执行更新(
                更新_sql, 
                (信息_jsonb, 当前时间, 线索id, 更新来源)
            )
        else:
            影响行数 = await 异步连接池实例.执行更新(
                更新_sql, 
                (信息_jsonb, 当前时间, 线索id)
            )
        
        if 影响行数 > 0:
            数据库日志器.info(f"成功更新线索信息，线索ID: {线索id}")
            return True
        else:
            数据库日志器.warning(f"更新线索信息未影响任何行，线索ID: {线索id}")
            return False
            
    except Exception as e:
        错误日志器.error(f"更新线索信息异常，线索ID: {线索id}, 错误: {str(e)}")
        return False


async def Postgre_分页获取线索列表(
    页码: int = 1,
    每页数量: int = 20,
    筛选条件: Optional[Dict[str, Any]] = None
) -> Tuple[List[Dict[str, Any]], int]:
    """
    分页获取线索列表

    Args:
        页码: 页码（从1开始）
        每页数量: 每页记录数
        筛选条件: 筛选条件字典

    Returns:
        (线索列表, 总记录数)
    """
    try:
        # 构建WHERE条件
        where_条件 = []
        参数列表 = []
        参数索引 = 1
        
        if 筛选条件:
            # 按联系方式类型筛选
            if 筛选条件.get("联系方式类型"):
                where_条件.append(f"c.类型 = ${参数索引}")
                参数列表.append(筛选条件["联系方式类型"])
                参数索引 += 1
            
            # 按来源筛选
            if 筛选条件.get("来源"):
                where_条件.append(f"l.来源 = ${参数索引}")
                参数列表.append(筛选条件["来源"])
                参数索引 += 1
            
            # 按联系方式内容搜索
            if 筛选条件.get("联系方式"):
                where_条件.append(f"c.联系方式 ILIKE ${参数索引}")
                参数列表.append(f"%{筛选条件['联系方式']}%")
                参数索引 += 1
            
            # 按信息中的名称搜索
            if 筛选条件.get("名称"):
                where_条件.append(f"l.信息->>'名称' ILIKE ${参数索引}")
                参数列表.append(f"%{筛选条件['名称']}%")
                参数索引 += 1
        
        where_clause = "WHERE " + " AND ".join(where_条件) if where_条件 else ""
        
        # 查询总数
        计数SQL = f"""
        SELECT COUNT(*) as total 
        FROM kol.线索表 l 
        LEFT JOIN kol.联系方式表 c ON l.联系方式id = c.id 
        {where_clause}
        """
        
        总数结果 = await 异步连接池实例.执行查询(计数SQL, tuple(参数列表))
        总数 = 总数结果[0]["total"] if 总数结果 else 0
        
        # 查询数据
        偏移量 = (页码 - 1) * 每页数量
        查询SQL = f"""
        SELECT 
            l.id, l.联系方式id, l.信息, l.来源, l.创建时间, l.更新时间,
            c.联系方式, c.类型 as 联系方式类型
        FROM kol.线索表 l
        LEFT JOIN kol.联系方式表 c ON l.联系方式id = c.id
        {where_clause}
        ORDER BY l.创建时间 DESC
        LIMIT ${参数索引} OFFSET ${参数索引 + 1}
        """
        
        # 添加分页参数
        参数列表.extend([每页数量, 偏移量])
        
        线索列表 = await 异步连接池实例.执行查询(查询SQL, tuple(参数列表))
        
        # 处理JSONB字段
        for 线索 in 线索列表:
            if 线索.get("信息"):
                try:
                    线索["信息"] = json.loads(线索["信息"]) if isinstance(线索["信息"], str) else 线索["信息"]
                except (json.JSONDecodeError, TypeError):
                    线索["信息"] = {}
        
        数据库日志器.debug(f"分页获取线索列表成功，页码: {页码}, 每页: {每页数量}, 总数: {总数}")
        return 线索列表, 总数
        
    except Exception as e:
        错误日志器.error(f"分页获取线索列表失败，页码: {页码}, 每页: {每页数量}, 错误: {str(e)}", exc_info=True)
        return [], 0


async def Postgre_检查线索是否存在(
    联系方式id: Optional[int], 
    信息_json_str: Optional[str]
) -> bool:
    """
    根据联系方式id和序列化后的信息JSON字符串检查线索是否已存在

    Args:
        联系方式id: 联系方式ID
        信息_json_str: 序列化后的信息JSON字符串

    Returns:
        True如果存在，否则False
    """
    try:
        query_base = "SELECT id FROM kol.线索表 WHERE "
        params = []
        conditions = []
        参数索引 = 1

        if 联系方式id is not None:
            conditions.append(f"联系方式id = ${参数索引}")
            params.append(联系方式id)
            参数索引 += 1
        else:
            conditions.append("联系方式id IS NULL")

        if 信息_json_str is not None:
            # 使用PostgreSQL的JSONB比较
            conditions.append(f"信息 = ${参数索引}::jsonb")
            params.append(信息_json_str)
            参数索引 += 1
        else:
            # 检查信息字段为NULL或空JSON对象的情况
            conditions.append("(信息 IS NULL OR 信息 = '{}'::jsonb OR 信息 = '[]'::jsonb)")

        query = query_base + " AND ".join(conditions)
        
        结果 = await 异步连接池实例.执行查询(query, tuple(params))
        
        存在 = bool(结果)
        数据库日志器.debug(f"检查线索是否存在: {存在}, 联系方式ID: {联系方式id}")
        return 存在
        
    except Exception as e:
        错误日志器.error(f"检查线索是否存在异常: {str(e)}")
        return False


async def Postgre_按名称检查线索是否存在(
    联系方式id: int,
    额外信息: Optional[Dict[str, Any]] = None,
    检查名称: bool = False,
    名称值: Optional[str] = None
) -> bool:
    """
    检查线索是否存在，支持按名称或完整信息检查

    Args:
        联系方式id: 联系方式ID
        额外信息: 额外信息字典
        检查名称: 是否只检查名称字段
        名称值: 要检查的名称值

    Returns:
        True如果存在，否则False
    """
    try:
        query_base = "SELECT id FROM kol.线索表 WHERE 联系方式id = $1"
        params = [联系方式id]
        参数索引 = 2

        if 检查名称 and 名称值 is not None:
            # 按名称检查
            query_condition = f"AND 信息->>'名称' = ${参数索引}"
            params.append(str(名称值))
            log_message_detail = f"名称: '{名称值}'"
        else:
            # 按完整信息JSON检查
            信息_json_str = json.dumps(额外信息, ensure_ascii=False, sort_keys=True) if 额外信息 is not None else None
            if 信息_json_str is None:
                query_condition = "AND 信息 IS NULL"
            else:
                query_condition = f"AND 信息 = ${参数索引}::jsonb"
                params.append(信息_json_str)
            
            log_message_detail = f"完整信息JSON: '{信息_json_str[:200] + '...' if 信息_json_str and len(信息_json_str) > 200 else 信息_json_str}'"

        query = query_base + " " + query_condition
        
        结果 = await 异步连接池实例.执行查询(query, tuple(params))
        
        存在 = bool(结果)
        数据库日志器.debug(f"按名称检查线索是否存在: {存在}, 联系方式ID: {联系方式id}, {log_message_detail}")
        return 存在
        
    except Exception as e:
        错误日志器.error(f"按名称检查线索是否存在异常: {str(e)}")
        return False
