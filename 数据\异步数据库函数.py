import asyncio
import secrets
import time
import traceback
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

from fastapi import Request

import 状态
from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例

# 导入统一日志系统
from 日志 import 安全日志器, 数据库日志器, 错误日志器

# =============================================================================
# 用户相关数据库操作函数
# =============================================================================


async def 异步获取用户_电话(电话: str) -> Optional[Dict[str, Any]]:
    """
    异步查询用户信息（包含状态字段）

    Args:
        电话: 用户手机号

    Returns:
        用户信息字典或None

    Raises:
        ConnectionError: 数据库连接异常时抛出，供上层认证系统处理
    """
    try:
        结果 = await 异步连接池实例.执行查询(
            "SELECT id, 昵称, password, 手机号, 状态 FROM 用户表 WHERE 手机号 = %s",
            (电话,),
        )
        return 结果[0] if 结果 else None
    except ConnectionError as conn_error:
        # 连接错误，重新抛出以便上层认证系统处理
        错误日志器.error(
            f"查询用户信息时数据库连接异常 (电话: {电话}): {str(conn_error)}"
        )
        raise conn_error
    except Exception as e:
        错误日志器.error(f"查询用户信息异常 (电话: {电话}): {str(e)}", exc_info=True)
        return None


async def 异步获取用户_id(user_id: int) -> Optional[Dict[str, Any]]:
    """
    通过用户ID异步获取用户信息

    Args:
        user_id: 用户ID

    Returns:
        用户信息字典或None

    Raises:
        ConnectionError: 数据库连接异常时抛出，供上层认证系统处理
    """
    try:
        结果 = await 异步连接池实例.执行查询(
            "SELECT * FROM 用户表 WHERE id = %s", (user_id,)
        )
        return 结果[0] if 结果 else None
    except ConnectionError as conn_error:
        # 连接错误，重新抛出以便上层认证系统处理
        错误日志器.error(
            f"获取用户信息时数据库连接异常 (用户ID: {user_id}): {str(conn_error)}"
        )
        raise conn_error
    except Exception as e:
        错误日志器.error(
            f"获取用户信息异常 (用户ID: {user_id}): {str(e)}", exc_info=True
        )
        return None


async def 异步用户是否存在_id(user_id: int) -> bool:
    """
    异步检查用户是否存在 - 增强版本，包含连接异常处理

    Args:
        user_id: 用户ID

    Returns:
        用户是否存在

    Raises:
        ConnectionError: 数据库连接异常时抛出，供上层认证系统处理
    """
    try:
        结果 = await 异步连接池实例.执行查询(
            "SELECT id FROM 用户表 WHERE id = %s", (user_id,)
        )
        return bool(结果)
    except ConnectionError as conn_error:
        # 连接错误，重新抛出以便上层认证系统处理
        错误日志器.error(
            f"查询用户存在性时数据库连接异常 (用户ID: {user_id}): {str(conn_error)}"
        )
        raise conn_error
    except Exception as e:
        # 其他异常，记录日志并返回False
        错误日志器.error(
            f"查询用户存在性异常 (用户ID: {user_id}): {str(e)}", exc_info=True
        )
        return False


async def 异步用户是否存在_电话(电话: str) -> bool:
    """异步检查用户是否存在（通过电话）- 不包括未注册状态用户"""
    try:
        结果 = await 异步连接池实例.执行查询(
            "SELECT id FROM 用户表 WHERE 手机号 = %s AND (状态 IS NULL OR 状态 != '未注册')",
            (电话,),
        )
        return bool(结果)
    except Exception as e:
        print(f"异步查询用户电话异常: {str(e)}")
        return False


async def 异步用户是否已注册_电话(电话: str) -> bool:
    """异步检查用户是否已完成注册（区分完全未注册和半注册状态）"""
    try:
        结果 = await 异步连接池实例.执行查询(
            "SELECT id, 状态 FROM 用户表 WHERE 手机号 = %s", (电话,)
        )
        if not 结果:
            return False

        用户状态 = 结果[0].get("状态")
        # 状态为空或不是"未注册"的用户视为已完成注册
        return 用户状态 != "未注册"
    except Exception as e:
        数据库日志器.error(f"异步查询用户注册状态异常: {str(e)}")
        return False


async def 异步创建用户(phone: str, hashed_password: str) -> int:
    """
    异步创建用户（返回用户ID）

    Args:
        phone: 用户手机号
        hashed_password: 加密后的密码

    Returns:
        创建的用户ID

    Raises:
        ConnectionError: 数据库连接异常
        Exception: 其他创建异常
    """
    try:
        用户ID = await 异步连接池实例.执行插入(
            "INSERT INTO 用户表 (手机号, password) VALUES (%s, %s)",
            (phone, hashed_password),
        )
        if 用户ID is None:
            raise Exception("创建用户失败，未返回用户ID")
        return 用户ID
    except ConnectionError as conn_error:
        错误日志器.error(f"创建用户时数据库连接异常 (电话: {phone}): {str(conn_error)}")
        raise conn_error
    except Exception as e:
        错误日志器.error(f"创建用户异常 (电话: {phone}): {str(e)}", exc_info=True)
        raise


async def 异步创建半注册用户(phone: str) -> int:
    """
    异步创建半注册用户（仅用于邀请未注册用户）
    创建状态为"未注册"的用户记录，只包含手机号和用户ID

    Args:
        phone: 手机号码，必须是11位数字

    Returns:
        int: 创建的用户ID，如果失败则抛出异常

    Raises:
        ValueError: 手机号格式不正确
        Exception: 数据库操作异常
    """
    try:
        # 【修复关键问题】增强参数验证
        if not phone or len(phone.strip()) == 0:
            数据库日志器.error("创建半注册用户失败：手机号为空")
            raise ValueError("手机号不能为空")

        phone = phone.strip()
        if len(phone) != 11 or not phone.isdigit():
            数据库日志器.error(f"创建半注册用户失败：手机号格式错误 ({phone})")
            raise ValueError("手机号格式不正确，必须是11位数字")

        # 检查是否已存在相同手机号的用户（包括半注册用户）
        现有用户 = await 异步获取用户_电话(phone)
        if 现有用户:
            数据库日志器.warning(
                f"手机号 {phone} 已存在用户记录，ID: {现有用户['id']}, 状态: {现有用户.get('状态', 'N/A')}"
            )
            # 如果是半注册用户，直接返回现有用户ID
            if 现有用户.get("状态") == "未注册":
                数据库日志器.info(f"返回现有半注册用户ID: {现有用户['id']}")
                return 现有用户["id"]
            else:
                raise ValueError(f"手机号 {phone} 已被注册")

        数据库日志器.info(f"开始创建半注册用户，手机号: {phone}")

        # 执行插入操作
        用户ID = await 异步连接池实例.执行插入(
            "INSERT INTO 用户表 (手机号, 状态) VALUES (%s, %s)", (phone, "未注册")
        )

        # 验证插入结果
        if not 用户ID or 用户ID <= 0:
            数据库日志器.error(f"创建半注册用户失败：数据库返回无效用户ID ({用户ID})")
            raise Exception(f"数据库插入失败，返回无效用户ID: {用户ID}")

        数据库日志器.info(f"成功创建半注册用户，手机号: {phone}, 用户ID: {用户ID}")
        return 用户ID

    except ValueError as ve:
        # 参数验证错误，直接抛出不记录到错误日志
        数据库日志器.warning(f"创建半注册用户参数错误: {ve}")
        raise
    except Exception as e:
        数据库日志器.error(f"异步创建半注册用户异常: {str(e)}, 手机号: {phone}")
        # 提供更友好的错误信息
        if "Duplicate entry" in str(e):
            raise Exception(f"手机号 {phone} 已被注册")
        else:
            raise Exception(f"创建用户记录失败: {str(e)}")


async def 异步获取用户会员状态(用户id: int) -> Optional[Dict]:
    """异步获取用户当前有效的会员身份及配额信息 - 基于会员关联表的新架构"""
    try:
        结果 = await 异步连接池实例.执行查询(
            """
            SELECT
                m.id AS 会员id,
                m.名称 AS 会员名称,
                um.到期时间 AS 到期时间,
                m.每月算力点 AS 算力点
            FROM 用户_会员_关联表 um
            JOIN 会员表 m ON um.会员id = m.id
            WHERE um.用户id = %s
                AND STR_TO_DATE(um.到期时间, '%%Y-%%m-%%d %%H:%%i:%%s') > NOW()
            ORDER BY m.id DESC
        """,
            (用户id,),
        )
        return 结果[0] if 结果 else None  # 取最高级别的会员身份
    except Exception as e:
        print(f"异步查询会员状态异常: {str(e)}")
        return None


async def 异步获取用户权限_id(user_id: int) -> List[Dict]:
    """异步获取用户完整权限列表 - 基于会员关联表的新架构"""
    try:
        return await 异步连接池实例.执行查询(
            """
            SELECT
                mp.权限id AS permission_id,
                um.开通时间 AS created_at,
                um.到期时间 AS expiry_date,
                p.名称 AS permission_name
            FROM
                用户_会员_关联表 AS um
            INNER JOIN
                会员_权限_关联表 AS mp ON um.会员id = mp.会员id
            INNER JOIN
                权限表 AS p ON mp.权限id = p.id
            WHERE
                um.用户id = %s
                AND STR_TO_DATE(um.到期时间, '%%Y-%%m-%%d %%H:%%i:%%s') > NOW()
        """,
            (user_id,),
        )
    except Exception as e:
        print(f"异步获取权限异常: {str(e)}")
        return []


async def 异步获取用户JWT认证信息(用户id: int) -> Dict[str, Any]:
    """
    获取用户的完整认证信息，用于JWT token生成
    包括：基本用户信息、会员信息、权限信息

    Args:
        用户id: 用户ID

    Returns:
        Dict: 包含用户完整认证信息的字典
    """
    try:
        # 1. 获取用户基本信息
        用户信息_sql = """
        SELECT id, 手机号, 昵称, is_admin 
        FROM 用户表 
        WHERE id = %s
        """
        用户基本信息 = await 异步连接池实例.执行查询(用户信息_sql, (用户id,))

        if not 用户基本信息:
            return {}

        用户信息 = 用户基本信息[0]

        # 2. 获取用户当前有效的会员信息
        会员信息_sql = """
        SELECT
            m.id AS 会员id,
            m.名称 AS 会员名称,
            um.到期时间 AS 到期时间,
            m.每月算力点 AS 算力点,
            m.可创建团队数 AS 可创建团队数,
            m.创建团队默认人数上限 AS 创建团队默认人数上限
        FROM 用户_会员_关联表 um
        JOIN 会员表 m ON um.会员id = m.id
        WHERE um.用户id = %s
            AND STR_TO_DATE(um.到期时间, '%%Y-%%m-%%d %%H:%%i:%%s') > NOW()
        ORDER BY m.id DESC
        LIMIT 1
        """
        会员结果 = await 异步连接池实例.执行查询(会员信息_sql, (用户id,))
        会员信息 = 会员结果[0] if 会员结果 else None

        # 3. 获取用户权限信息
        权限信息_sql = """
        SELECT DISTINCT
            p.id AS 权限id,
            p.名称 AS 权限名称,
            p.描述 AS 权限描述
        FROM 用户_会员_关联表 AS um
        INNER JOIN 会员_权限_关联表 AS mp ON um.会员id = mp.会员id
        INNER JOIN 权限表 AS p ON mp.权限id = p.id
        WHERE um.用户id = %s
            AND STR_TO_DATE(um.到期时间, '%%Y-%%m-%%d %%H:%%i:%%s') > NOW()
        ORDER BY p.id
        """
        权限结果 = await 异步连接池实例.执行查询(权限信息_sql, (用户id,))

        # 4. 组装完整的认证信息
        认证信息 = {
            # 基本用户信息
            "id": 用户信息["id"],
            "手机号": 用户信息["手机号"],
            "昵称": 用户信息["昵称"],
            "is_admin": bool(用户信息.get("is_admin", False)),
            # 会员信息
            "会员信息": {
                "会员id": 会员信息["会员id"] if 会员信息 else None,
                "会员名称": 会员信息["会员名称"] if 会员信息 else "免费用户",
                "到期时间": str(会员信息["到期时间"])
                if 会员信息 and 会员信息["到期时间"]
                else None,
                "算力点": 会员信息["算力点"] if 会员信息 else 0,
                "可创建团队数": 会员信息["可创建团队数"] if 会员信息 else 0,
                "创建团队默认人数上限": 会员信息["创建团队默认人数上限"]
                if 会员信息
                else 0,
            },
            # 权限信息
            "权限信息": [
                {
                    "权限id": 权限["权限id"],
                    "权限名称": 权限["权限名称"],
                    "权限描述": 权限.get("权限描述", ""),
                }
                for 权限 in 权限结果
            ],
        }

        return 认证信息

    except Exception as e:
        print(f"获取用户JWT认证信息异常: 用户ID={用户id}, 错误={str(e)}")
        # 返回最基本的用户信息，确保JWT能正常生成
        try:
            基本信息_sql = "SELECT id, 手机号, 昵称, is_admin FROM 用户表 WHERE id = %s"
            基本结果 = await 异步连接池实例.执行查询(基本信息_sql, (用户id,))
            if 基本结果:
                基本信息 = 基本结果[0]
                return {
                    "id": 基本信息["id"],
                    "手机号": 基本信息["手机号"],
                    "昵称": 基本信息["昵称"],
                    "is_admin": bool(基本信息.get("is_admin", False)),
                    "会员信息": {
                        "会员id": None,
                        "会员名称": "免费用户",
                        "到期时间": None,
                        "算力点": 0,
                        "可创建团队数": 0,
                        "创建团队默认人数上限": 0,
                    },
                    "权限信息": [],
                }
        except:
            pass
        return {}


async def 异步获取用户昵称_id(用户id: int) -> Optional[str]:
    """异步获取用户昵称_id"""
    try:
        结果 = await 异步连接池实例.执行查询(
            "SELECT 昵称 FROM 用户表 WHERE id = %s", (用户id,)
        )
        return 结果[0]["昵称"] if 结果 else None
    except Exception as e:
        print(f"异步获取用户昵称_id异常: {str(e)}")
        return None


async def 异步获取用户邀请人ID(用户id: int) -> Optional[int]:
    """异步获取用户的邀请人ID"""
    try:
        sql = "SELECT 邀请人 FROM 用户表 WHERE id = %s"
        结果 = await 异步连接池实例.执行查询(sql, (用户id,))
        if 结果 and 结果[0] and 结果[0]["邀请人"] is not None:
            数据库日志器.debug(
                f"成功获取用户ID {用户id} 的邀请人ID: {结果[0]['邀请人']}。"
            )
            return int(结果[0]["邀请人"])
        数据库日志器.debug(f"用户ID {用户id} 没有邀请人ID，或查询结果为空。")
        return None
    except Exception as e:
        错误日志器.error(
            f"异步获取用户ID {用户id} 的邀请人ID异常: {str(e)}", exc_info=True
        )
        return None


# 验证码系统
class 异步验证码管理器:
    def __init__(self):
        self.缓存: Dict[str, dict] = {}
        self.锁 = asyncio.Lock()  # 使用异步锁
        self.最大容量 = 1000  # 防止内存溢出

    async def 存储验证码(self, phone: str, code: str, 场景: str, 有效期秒数: int = 600):
        """异步存储验证码"""
        async with self.锁:
            # 自动清理10%的旧数据
            if len(self.缓存) > self.最大容量 * 0.9:
                await self._自动清理()

            key = f"{场景}:{phone}"
            expire_time = time.time() + 有效期秒数

            self.缓存[key] = {"code": code, "expire": expire_time}
        print("验证码列表", self.缓存)

    async def 验证验证码(self, phone: str, code: str, 场景: str) -> dict:
        """异步验证验证码，返回详细状态"""
        print(f"验证验证码: {phone}, {code}, {场景}")
        key = f"{场景}:{phone}"

        async with self.锁:
            print("验证码列表", self.缓存)
            stored = self.缓存.get(key)
            print(f"获取验证码: {key}, {stored}")
            if not stored:
                print(f"验证码不存在: {key}")
                return {
                    "valid": False,
                    "status": 状态.短信服务.验证码错误,
                    "message": "验证码不存在",
                }

            # 自动清理过期
            if time.time() > stored["expire"]:
                print(f"当前时间: {time.time()}, 存储的过期时间: {stored['expire']}")
                print(f"验证码已过期: {key}")
                del self.缓存[key]
                return {
                    "valid": False,
                    "status": 状态.短信服务.验证码过期,
                    "message": "验证码已过期",
                }

            try:
                # 安全比较
                code_match = secrets.compare_digest(code, stored["code"])
                if code_match:
                    print(f"验证码验证成功: {key}")
                    del self.缓存[key]  # 验证成功后删除
                    return {
                        "valid": True,
                        "status": 状态.通用.成功_旧,
                        "message": "验证码正确",
                    }
                return {
                    "valid": False,
                    "status": 状态.短信服务.验证码错误,
                    "message": "验证码错误",
                }
            except Exception as e:
                print(f"验证码验证过程中出现异常: {str(e)}")
                return {
                    "valid": False,
                    "status": 状态.通用.服务器错误,
                    "message": f"验证码验证异常: {str(e)}",
                }

    async def _自动清理(self):
        now = time.time()
        to_delete = []
        # 找出过期的
        for key, data in self.缓存.items():
            if data["expire"] < now:
                to_delete.append(key)
        # 清理超过最大容量的
        if len(self.缓存) > self.最大容量:
            excess = len(self.缓存) - self.最大容量
            to_delete.extend(list(self.缓存.keys())[:excess])
        # 执行删除
        for key in to_delete:
            del self.缓存[key]


# 全局单例实例
异步验证码系统 = 异步验证码管理器()


# 订单相关功能
async def 异步获取用户订单_id(订单id: int, 用户id: int) -> Optional[Dict]:
    """异步获取用户指定订单"""
    try:
        结果 = await 异步连接池实例.执行查询(
            "SELECT * FROM 支付订单表 WHERE id = %s AND 用户id = %s", (订单id, 用户id)
        )
        return 结果[0] if 结果 else None
    except Exception as e:
        print(f"异步获取订单异常: {str(e)}")
        return None


# 添加缺少的函数
async def 异步获取用户密码哈希(用户id: int) -> Optional[str]:
    """异步获取用户密码哈希"""
    try:
        结果 = await 异步连接池实例.执行查询(
            "SELECT password FROM 用户表 WHERE id = %s", (用户id,)
        )
        return 结果[0]["password"] if 结果 else None
    except Exception as e:
        print(f"异步获取用户密码哈希异常: {str(e)}")
        return None


async def 异步更新用户密码(用户id: int, 新密码: str) -> bool:
    """异步更新用户密码"""
    try:
        await 异步连接池实例.执行更新(
            "UPDATE 用户表 SET password = %s WHERE id = %s", (新密码, 用户id)
        )
        return True
    except Exception as e:
        print(f"异步更新用户密码异常: {str(e)}")
        return False


async def 异步查询激活码信息(code: str) -> Optional[Dict]:
    """异步查询激活码信息"""
    try:
        结果 = await 异步连接池实例.执行查询(
            """
            SELECT
                id, 激活码, 激活码类型表id, 激活用户id, 备注, 是否为一次性激活, 创建时间, 使用时间
            FROM
                激活码表
            WHERE
                激活码 = %s
            """,
            (code,),
        )
        return 结果[0] if 结果 else None
    except Exception as e:
        print(f"异步查询激活码信息异常: {str(e)}")
        return None


async def 异步查询激活码类型(类型id: int) -> Optional[Dict]:
    """异步查询激活码类型"""
    try:
        结果 = await 异步连接池实例.执行查询(
            """
            SELECT
                id, 名称, 描述, 价格, 会员表id, 会员天数
            FROM
                激活码类型表
            WHERE
                id = %s
            """,
            (类型id,),
        )
        return 结果[0] if 结果 else None
    except Exception as e:
        print(f"异步查询激活码类型异常: {str(e)}")
        return None


async def 异步设置用户指定权限时间(用户id: int, 会员id: int, 增加秒数: int) -> bool:
    """异步设置用户会员时间 - 基于会员关联表的新架构"""
    try:
        会员记录 = await 异步连接池实例.执行查询(
            """
            SELECT
                 会员id, 到期时间
            FROM
                用户_会员_关联表
            WHERE
                用户id = %s AND 会员id = %s
            """,
            (用户id, 会员id),
        )

        now = datetime.now()

        if 会员记录:
            # 已有会员，延长时间
            current_expiry_str = 会员记录[0].get("到期时间")
            if current_expiry_str:
                try:
                    # 检查到期时间的类型，如果已经是datetime对象就直接使用
                    if isinstance(current_expiry_str, datetime):
                        current_expiry = current_expiry_str
                    else:
                        current_expiry = datetime.strptime(
                            current_expiry_str, "%Y-%m-%d %H:%M:%S"
                        )
                    # 判断当前会员是否过期
                    if current_expiry > now:
                        # 会员未过期，只增加到期时间
                        expiry_date = current_expiry + timedelta(seconds=增加秒数)
                        # 更新现有记录（只更新到期时间）
                        await 异步连接池实例.执行更新(
                            """
                            UPDATE 用户_会员_关联表
                            SET 到期时间 = %s
                            WHERE 用户id = %s AND 会员id = %s
                            """,
                            (expiry_date.strftime("%Y-%m-%d %H:%M:%S"), 用户id, 会员id),
                        )
                    else:
                        # 会员已过期，同时修改开通时间和到期时间
                        expiry_date = now + timedelta(seconds=增加秒数)
                        # 更新现有记录（同时更新开通时间和到期时间）
                        await 异步连接池实例.执行更新(
                            """
                            UPDATE 用户_会员_关联表
                            SET 开通时间 = %s, 到期时间 = %s
                            WHERE 用户id = %s AND 会员id = %s
                            """,
                            (
                                now.strftime("%Y-%m-%d %H:%M:%S"),
                                expiry_date.strftime("%Y-%m-%d %H:%M:%S"),
                                用户id,
                                会员id,
                            ),
                        )
                except Exception:
                    # 时间格式解析失败，按过期处理
                    expiry_date = now + timedelta(seconds=增加秒数)
                    await 异步连接池实例.执行更新(
                        """
                        UPDATE 用户_会员_关联表
                        SET 开通时间 = %s, 到期时间 = %s
                        WHERE 用户id = %s AND 会员id = %s
                        """,
                        (
                            now.strftime("%Y-%m-%d %H:%M:%S"),
                            expiry_date.strftime("%Y-%m-%d %H:%M:%S"),
                            用户id,
                            会员id,
                        ),
                    )
            else:
                # 到期时间为空，按过期处理
                expiry_date = now + timedelta(seconds=增加秒数)
                await 异步连接池实例.执行更新(
                    """
                    UPDATE 用户_会员_关联表
                    SET 开通时间 = %s, 到期时间 = %s
                    WHERE 用户id = %s AND 会员id = %s
                    """,
                    (
                        now.strftime("%Y-%m-%d %H:%M:%S"),
                        expiry_date.strftime("%Y-%m-%d %H:%M:%S"),
                        用户id,
                        会员id,
                    ),
                )
        else:
            # 新增会员记录
            expiry_date = now + timedelta(seconds=增加秒数)
            await 异步连接池实例.执行插入(
                """
                INSERT INTO 用户_会员_关联表
                    (用户id, 会员id, 开通时间, 到期时间)
                VALUES
                    (%s, %s, %s, %s)
                """,
                (
                    用户id,
                    会员id,
                    now.strftime("%Y-%m-%d %H:%M:%S"),
                    expiry_date.strftime("%Y-%m-%d %H:%M:%S"),
                ),
            )

        return True
    except Exception as e:
        print(f"异步设置用户会员时间异常: {str(e)}")
        return False


async def 异步查询激活记录(激活码id: int, 用户id: int = None) -> Optional[Dict]:
    """
    异步查询激活记录

    Args:
        激活码id: 激活码ID
        用户id: 用户ID（可选，用于查询特定用户的激活记录）

    Returns:
        激活记录信息或None
    """
    try:
        if 用户id:
            # 查询特定用户是否使用过该激活码
            查询 = """
            SELECT id, 用户id, 激活码表id, 使用时间
            FROM 激活记录表
            WHERE 激活码表id = %s AND 用户id = %s
            """
            参数 = (激活码id, 用户id)
        else:
            # 查询该激活码是否被任何用户使用过
            查询 = """
            SELECT id, 用户id, 激活码表id, 使用时间
            FROM 激活记录表
            WHERE 激活码表id = %s
            """
            参数 = (激活码id,)

        结果 = await 异步连接池实例.执行查询(查询, 参数)
        return 结果[0] if 结果 else None
    except Exception as e:
        print(f"异步查询激活记录异常: {str(e)}")
        return None


async def 异步插入激活记录(激活码id: int, 用户id: int) -> bool:
    """
    异步插入激活记录

    Args:
        激活码id: 激活码ID
        用户id: 用户ID

    Returns:
        是否插入成功
    """
    try:
        await 异步连接池实例.执行插入(
            """
            INSERT INTO 激活记录表 (用户id, 激活码表id, 使用时间)
            VALUES (%s, %s, NOW())
            """,
            (用户id, 激活码id),
        )
        return True
    except Exception as e:
        print(f"异步插入激活记录异常: {str(e)}")
        return False


async def 异步设置激活码使用状态(code: str, 用户id: int) -> bool:
    """异步设置激活码使用状态（仅用于一次性激活码）"""
    try:
        await 异步连接池实例.执行更新(
            """
            UPDATE 激活码表
            SET 激活用户id = %s, 使用时间 = NOW()
            WHERE 激活码 = %s AND 激活用户id IS NULL AND 使用时间 IS NULL
            """,
            (用户id, code),
        )
        return True
    except Exception as e:
        print(f"异步设置激活码使用状态异常: {str(e)}")
        return False


async def 异步查询用户指定会员到期时间(用户id: int, 会员id: int) -> Optional[datetime]:
    """异步查询用户指定会员到期时间 - 兼容新旧权限架构"""
    try:
        # 优先查询新表：用户_会员_关联表
        结果 = await 异步连接池实例.执行查询(
            """
            SELECT
                到期时间
            FROM
                用户_会员_关联表
            WHERE
                用户id = %s AND 会员id = %s
            """,
            (用户id, 会员id),
        )

        # 如果新表有记录且有效，直接返回
        if 结果 and 结果[0]["到期时间"]:
            try:
                到期时间 = datetime.strptime(结果[0]["到期时间"], "%Y-%m-%d %H:%M:%S")
                print(f"用户{用户id}在新表中找到会员权限，到期时间: {到期时间}")
                return 到期时间
            except Exception as parse_e:
                print(f"新表日期解析失败: {str(parse_e)}")

        # 如果新表没有记录，查询旧表：用户权限表（兼容性检查）
        print(f"用户{用户id}在新表中无会员记录，查询旧表...")
        旧表结果 = await 异步连接池实例.执行查询(
            """
            SELECT
                expiry_date
            FROM
                用户权限表
            WHERE
                user_id = %s AND permission_id = %s
            """,
            (用户id, 会员id),
        )

        # 如果旧表有记录且有效，返回该时间
        if 旧表结果 and 旧表结果[0]["expiry_date"]:
            try:
                旧表到期时间 = 旧表结果[0]["expiry_date"]
                # 旧表字段已经是datetime类型，无需转换
                if isinstance(旧表到期时间, datetime):
                    print(f"用户{用户id}在旧表中找到会员权限，到期时间: {旧表到期时间}")
                    return 旧表到期时间
                else:
                    # 如果是字符串格式，尝试解析
                    解析后时间 = datetime.strptime(
                        str(旧表到期时间), "%Y-%m-%d %H:%M:%S"
                    )
                    print(
                        f"用户{用户id}在旧表中找到会员权限（解析后），到期时间: {解析后时间}"
                    )
                    return 解析后时间
            except Exception as old_parse_e:
                print(f"旧表日期解析失败: {str(old_parse_e)}")

        print(f"用户{用户id}在新旧表中均无有效会员权限记录")
        return None

    except Exception as e:
        print(f"异步查询用户指定会员到期时间异常: {str(e)}")
        return None


async def 异步更新达人数据(uid_number: str, account_douyin: str, nickname: str) -> bool:
    """
    异步更新达人数据

    Args:
        uid_number: 达人UID
        account_douyin: 抖音账号
        nickname: 昵称

    Returns:
        更新是否成功
    """
    try:
        # 生成10位时间戳
        当前时间戳 = int(time.time())

        # 检查达人是否已存在
        现有达人 = await 异步连接池实例.执行查询(
            "SELECT id FROM kol.达人表 WHERE uid_number = %s", (uid_number,)
        )

        if 现有达人:
            # 更新现有达人
            影响行数 = await 异步连接池实例.执行更新(
                """
                UPDATE kol.达人表
                SET account_douyin = %s, nickname = %s, update_time = %s
                WHERE uid_number = %s
                """,
                (account_douyin, nickname, 当前时间戳, uid_number),
            )
            数据库日志器.info(
                f"更新达人数据成功: UID={uid_number}, 影响行数={影响行数}"
            )
        else:
            # 创建新达人
            达人ID = await 异步连接池实例.执行插入(
                """
                INSERT INTO kol.达人表
                    (uid_number, account_douyin, nickname, update_time)
                VALUES
                    (%s, %s, %s, %s)
                """,
                (uid_number, account_douyin, nickname, 当前时间戳),
            )
            数据库日志器.info(f"创建达人数据成功: UID={uid_number}, 达人ID={达人ID}")

        return True
    except ConnectionError as conn_error:
        错误日志器.error(
            f"更新达人数据时数据库连接异常 (UID: {uid_number}): {str(conn_error)}"
        )
        return False
    except Exception as e:
        错误日志器.error(
            f"更新达人数据异常 (UID: {uid_number}): {str(e)}", exc_info=True
        )
        return False


async def 异步获取店铺ID(shop_id: str) -> Optional[int]:
    """异步获取店铺ID"""
    try:
        结果 = await 异步连接池实例.执行查询(
            "SELECT id FROM 店铺 WHERE shop_id = %s", (shop_id,)
        )
        return 结果[0]["id"] if 结果 else None
    except Exception as e:
        print(f"异步获取店铺ID异常: {str(e)}")
        return None


async def 异步插入店铺数据(shop_id: str, shop_name: str, avatar: str) -> Optional[int]:
    """异步插入店铺数据"""
    try:
        return await 异步连接池实例.执行插入(
            """
            INSERT INTO 店铺 
                (shop_id, shop_name, avatar) 
            VALUES 
                (%s, %s, %s)
            """,
            (shop_id, shop_name, avatar),
        )
    except Exception as e:
        print(f"异步插入店铺数据异常: {str(e)}")
        return None


async def 异步关联用户店铺(用户id: int, 店铺id: int) -> bool:
    """异步关联用户和店铺"""
    try:
        # 检查关联是否已存在
        现有关联 = await 异步连接池实例.执行查询(
            "SELECT 用户ID FROM 用户_店铺 WHERE 用户id = %s AND 店铺id = %s",
            (用户id, 店铺id),
        )

        if not 现有关联:
            # 创建新关联
            await 异步连接池实例.执行插入(
                """
                INSERT INTO 用户_店铺 
                    (用户id, 店铺id) 
                VALUES 
                    (%s, %s)
                """,
                (用户id, 店铺id),
            )

        return True
    except Exception as e:
        print(f"异步关联用户店铺异常: {str(e)}")
        return False


async def 异步更新微信对接进度(
    数据: dict, 更新字段名列表: Optional[List[str]] = None
) -> Optional[int]:
    """
    更新微信对接进度信息，如果记录不存在则创建。

    参数:
        数据: 包含要更新的字段和值的字典。必须包含 '我方微信号ID', '对方微信号ID', '合作产品ID', '用户ID'。
        更新字段名列表: 可选，需要更新的字段名称列表 (例如 ['意向状态', '样品状态', '更新时间']).
                        如果为 None, 则默认只更新 '更新时间'.

    返回:
        更新或插入后的记录ID (int)，如果操作失败则返回 None。
    """
    try:
        # 检查必须的ID字段
        必要ID字段 = ["我方微信号ID", "对方微信号ID", "合作产品ID", "用户ID"]
        for 字段 in 必要ID字段:
            if not 数据.get(字段):
                raise ValueError(f"缺少必要的ID字段: {字段}")

        # 获取当前数据库中的记录，用于检查状态是否发生变化
        当前记录 = None
        async with 异步连接池实例.获取连接() as 连接:
            async with 连接.cursor() as 游标:
                await 游标.execute(
                    "SELECT * FROM 微信产品对接进度表 WHERE 我方微信号ID = %s AND 对方微信号ID = %s AND 合作产品ID = %s",
                    (
                        数据["我方微信号ID"],
                        数据["对方微信号ID"],
                        int(数据["合作产品ID"]),
                    ),
                )
                当前记录 = await 游标.fetchone()

        # 检查意向状态和样品状态是否发生变化，如果变化，添加对应的时间戳更新
        实际更新字段名列表 = 更新字段名列表[:] if 更新字段名列表 else ["更新时间"]
        当前时间 = datetime.now()

        # 1. 检查意向状态是否发生变化
        if "意向状态" in 实际更新字段名列表 and "意向状态" in 数据:
            # 如果是新记录或者意向状态发生了变化
            if 当前记录 is None or (
                "意向状态" in 数据 and 数据["意向状态"] != 当前记录["意向状态"]
            ):
                # 添加意向状态更新时间字段
                if "意向状态更新时间" not in 实际更新字段名列表:
                    实际更新字段名列表.append("意向状态更新时间")
                    数据["意向状态更新时间"] = 当前时间
                    print(
                        f"意向状态从 {当前记录['意向状态'] if 当前记录 else '无'} 变为 {数据['意向状态']}，更新意向状态更新时间"
                    )

        # 2. 检查样品状态是否发生变化
        if "样品状态" in 实际更新字段名列表 and "样品状态" in 数据:
            # 如果是新记录或者样品状态发生了变化
            if 当前记录 is None or (
                "样品状态" in 数据 and 数据["样品状态"] != 当前记录["样品状态"]
            ):
                # 添加样品状态更新时间字段
                if "样品状态更新时间" not in 实际更新字段名列表:
                    实际更新字段名列表.append("样品状态更新时间")
                    数据["样品状态更新时间"] = 当前时间
                    print(
                        f"样品状态从 {当前记录['样品状态'] if 当前记录 else '无'} 变为 {数据['样品状态']}，更新样品状态更新时间"
                    )

        # --- 插入部分 ---
        # 定义插入操作需要的所有字段 (包括用户ID)
        插入目标字段列表 = [
            "我方微信号ID",
            "对方微信号ID",
            "合作产品ID",
            "用户ID",
            "回复状态",
            "意向状态",
            "样品状态",
            "排期状态",
            "排期开始时间",
            "排期结束时间",
            "开播状态",
            "销售额",
            "创建时间",
            "更新时间",  # 插入时设置创建和更新时间
            "意向状态更新时间",
            "样品状态更新时间",  # 添加意向状态和样品状态的更新时间字段
        ]
        插入参数值列表 = []

        for 字段 in 插入目标字段列表:
            if 字段 == "创建时间" or 字段 == "更新时间":
                插入参数值列表.append(当前时间)
            elif 字段 == "意向状态更新时间" and "意向状态" in 数据:
                # 如果设置了意向状态，新记录时也设置意向状态更新时间
                插入参数值列表.append(数据.get("意向状态更新时间", 当前时间))
            elif 字段 == "样品状态更新时间" and "样品状态" in 数据:
                # 如果设置了样品状态，新记录时也设置样品状态更新时间
                插入参数值列表.append(数据.get("样品状态更新时间", 当前时间))
            else:
                # 确保获取的合作产品ID是整数
                if 字段 == "合作产品ID":
                    产品ID值 = 数据.get(字段)
                    if 产品ID值 is None:
                        raise ValueError("合作产品ID不能为空")
                    try:
                        插入参数值列表.append(int(产品ID值))
                    except (ValueError, TypeError):
                        raise ValueError(f"合作产品ID必须是有效的整数: {产品ID值}")
                else:
                    插入参数值列表.append(数据.get(字段))

        插入字段名字符串 = ", ".join(
            f"`{字段}`" for 字段 in 插入目标字段列表
        )  # 使用反引号避免关键字冲突
        插入占位符字符串 = ", ".join(["%s"] * len(插入目标字段列表))

        # --- 更新部分 (ON DUPLICATE KEY UPDATE) ---
        更新设置列表 = []
        更新参数值列表 = []

        for 字段 in 实际更新字段名列表:
            # 确保字段名在数据字典中，或者就是 '更新时间'
            if (
                字段 in 数据
                or 字段 == "更新时间"
                or 字段 == "意向状态更新时间"
                or 字段 == "样品状态更新时间"
            ):
                更新设置列表.append(f"`{字段}` = %s")
                if 字段 == "更新时间":
                    更新参数值列表.append(当前时间)  # 更新时也用当前时间
                elif 字段 == "意向状态更新时间":
                    更新参数值列表.append(数据.get("意向状态更新时间", 当前时间))
                elif 字段 == "样品状态更新时间":
                    更新参数值列表.append(数据.get("样品状态更新时间", 当前时间))
                else:
                    更新参数值列表.append(数据.get(字段))
            else:
                print(f"警告：尝试更新的字段 '{字段}' 不在传入的数据字典中，已跳过。")

        # 如果更新设置列表为空(例如传入了无效字段名)，至少要更新时间
        if not 更新设置列表:
            更新设置列表.append("`更新时间` = %s")
            更新参数值列表.append(当前时间)

        更新设置字符串 = ", ".join(更新设置列表)

        # --- 构建最终SQL ---
        # --- 修改：使用 AS new_values 别名 ---
        # 注意：别名更新需要显式列出字段，不能简单替换字符串
        update_clauses = []
        for 字段 in 实际更新字段名列表:
            # 避免在更新子句中再次设置创建时间
            if 字段 != "创建时间":
                # 如果字段名包含特殊字符或关键字，反引号是好习惯
                update_clauses.append(f"`{字段}` = new_values.`{字段}`")

        更新设置字符串_别名 = ", ".join(update_clauses)
        # 如果更新列表为空（只更新了更新时间，且创建时间也不应在update中），确保至少更新了更新时间
        if not 更新设置字符串_别名:
            更新设置字符串_别名 = (
                "`更新时间` = new_values.`更新时间`"  # 确保更新时间被更新
            )

        sql = f"""
            INSERT INTO 微信产品对接进度表 ({插入字段名字符串}) 
            VALUES ({插入占位符字符串})
            AS new_values 
            ON DUPLICATE KEY UPDATE {更新设置字符串_别名}
        """
        # --- 结束修改 ---

        # --- 组合所有参数 (插入参数) ---
        # 使用别名后，只需要插入部分的参数
        所有参数 = tuple(插入参数值列表)  # 确保是元组

        print(f"执行SQL: {sql}")
        print(f"使用参数: {所有参数}")

        # --- 执行更新 ---
        async with 异步连接池实例.获取连接() as 连接:
            async with 连接.cursor() as 游标:
                影响行数 = await 游标.execute(sql, 所有参数)
                await 连接.commit()  # 提交事务

                # --- 获取并返回ID ---
                if 影响行数 >= 0:  # 0表示更新但无变化，1表示插入，2表示更新且有变化
                    # 不论是插入还是更新，都根据唯一键查询ID
                    await 游标.execute(
                        "SELECT id FROM 微信产品对接进度表 WHERE 我方微信号ID = %s AND 对方微信号ID = %s AND 合作产品ID = %s",
                        (
                            数据["我方微信号ID"],
                            数据["对方微信号ID"],
                            int(数据["合作产品ID"]),
                        ),  # 确保产品ID是整数
                    )
                    结果 = await 游标.fetchone()
                    if 结果:
                        return 结果["id"]
                    else:
                        print(
                            f"警告：Upsert操作后未能查询到记录，唯一键：{数据['我方微信号ID']}-{数据['对方微信号ID']}-{数据['合作产品ID']}"
                        )
                        return None  # 查询不到ID，说明可能有问题
                else:
                    # 影响行数 < 0 通常表示错误
                    print(f"警告：Upsert操作影响行数异常: {影响行数}")
                    return None  # 操作未成功

    except ValueError as ve:
        print(f"更新微信对接进度校验失败: {str(ve)}")
        # raise # 可以选择继续抛出或者返回None
        return None
    except Exception as e:
        错误详情 = traceback.format_exc()  # 获取完整堆栈信息
        print(f"更新微信对接进度数据库操作失败: {str(e)}\n{错误详情}")
        # raise  # 可以选择继续抛出或者返回None
        return None


async def 异步提交联系方式(
    联系方式: str, 类型: str, 来源: str, 来源用户: Optional[int] = None
) -> int:
    """异步处理联系方式提交（存在则返回id，不存在则插入）"""
    try:
        # 检查是否存在
        现有记录 = await 异步连接池实例.执行查询(
            "SELECT id FROM 联系方式表 WHERE 联系方式 = $1 AND 类型 = $2",
            (联系方式, 类型 or "微信"),
        )

        if 现有记录:
            return 现有记录[0]["id"]

        # 插入新记录
        插入ID = await 异步连接池实例.执行插入(
            """
            INSERT INTO 联系方式表
                (联系方式, 类型, 来源)
            VALUES
                ($1, $2, $3)
            """,
            (联系方式, 类型 or "微信", 来源),
        )
        if 插入ID is None:
            raise Exception("插入联系方式失败，未返回ID")
        return 插入ID
    except ConnectionError as conn_error:
        错误日志器.error(f"提交联系方式时数据库连接异常: {str(conn_error)}")
        raise conn_error
    except Exception as e:
        错误日志器.error(f"提交联系方式异常: {str(e)}", exc_info=True)
        raise


async def 异步查询微信对接进度(我方微信号: str, 对方微信号: str) -> List[Dict]:
    """
    根据我方微信号和对方微信号查询所有微信对接进度信息

    参数:
        我方微信号: 我方微信号
        对方微信号: 对方微信号

    返回:
        包含对接进度和微信信息的字典数据列表
    """
    try:
        # 首先从微信信息表查询我方和对方微信号的ID
        我方微信号ID = await 异步连接池实例.执行查询(
            "SELECT id FROM 微信信息表 WHERE 微信号 = $1", (我方微信号,)
        )

        对方微信号ID = await 异步连接池实例.执行查询(
            "SELECT id FROM 微信信息表 WHERE 微信号 = $1", (对方微信号,)
        )

        if not 我方微信号ID or not 对方微信号ID:
            return []

        我方ID = 我方微信号ID[0]["id"]
        对方ID = 对方微信号ID[0]["id"]

        # 查询微信产品对接进度表 - 不再限制合作产品ID
        结果 = await 异步连接池实例.执行查询(
            """
            SELECT 
                进度.回复状态,
                进度.意向状态,
                进度.样品状态,
                进度.排期状态,
                进度.排期开始时间,
                进度.排期结束时间,
                进度.开播状态,
                进度.销售额,
                进度.合作产品ID,
                我方.微信号 AS 我方微信号,
                对方.微信号 AS 对方微信号
            FROM 
                微信产品对接进度表 AS 进度
            JOIN 
                微信信息表 AS 我方 ON 进度.我方微信号ID = 我方.id
            JOIN 
                微信信息表 AS 对方 ON 进度.对方微信号ID = 对方.id
            WHERE
                进度.我方微信号ID = $1 AND
                进度.对方微信号ID = $2
            """,
            (我方ID, 对方ID),
        )

        return 结果 if 结果 else []

    except Exception as e:
        print(f"查询微信对接进度异常: {str(e)}")
        raise


async def 异步获取或创建微信ID(微信号: str) -> dict:
    """根据微信号获取微信ID，如果不存在则创建。"""
    # 注意：表名和列名需要根据实际情况修改
    try:
        # 先尝试查找现有记录
        查询语句 = "SELECT id FROM 微信信息表 WHERE 微信号 = $1"
        结果 = await 异步连接池实例.执行查询(查询语句, (微信号,))

        if 结果:
            # 已存在记录
            return {"id": 结果[0]["id"], "是否新增": False}

        # 不存在则创建新记录
        sql = """
            INSERT INTO 微信信息表 (微信号)
            VALUES ($1)
        """
        新ID = await 异步连接池实例.执行插入(sql, (微信号,))
        return {"id": 新ID, "是否新增": True}

    except Exception as e:
        print(f"异步获取或创建微信ID异常: {str(e)}")
        raise


async def 异步添加微信好友并生成识别ID(我方微信号ID: int, 对方微信号ID: int) -> dict:
    """
    将两个微信号ID添加到微信好友表中，并生成识别ID

    参数:
        我方微信号ID: 我方微信号在数据库中的ID
        对方微信号ID: 对方微信号在数据库中的ID

    返回:
        包含识别ID的字典
    """
    try:
        # 首先查询是否已存在好友关系
        查询语句 = """
            SELECT 识别id FROM 微信好友表
            WHERE 我方微信号id = $1 AND 对方微信号id = $2
        """
        结果 = await 异步连接池实例.执行查询(
            查询语句, (我方微信号ID, 对方微信号ID)
        )

        if 结果:
            # 已存在记录，直接返回现有的识别ID
            return {"识别id": 结果[0]["识别id"], "是否新增": False}

        # 获取当前我方微信号的最大识别ID
        最大ID查询 = """
            SELECT MAX(识别id) as 最大id FROM 微信好友表
            WHERE 我方微信号id = $1
        """
        最大ID结果 = await 异步连接池实例.执行查询(最大ID查询, (我方微信号ID,))

        # 确定新的识别ID
        新识别ID = 1  # 默认从1开始
        if 最大ID结果 and 最大ID结果[0]["最大id"] is not None:
            新识别ID = 最大ID结果[0]["最大id"] + 1

        # 插入新的好友关系，设置好友入库时间为当前时间
        插入语句 = """
            INSERT INTO 微信好友表 (我方微信号id, 对方微信号id, 识别id, 好友入库时间)
            VALUES ($1, $2, $3, CURRENT_TIMESTAMP)
        """
        await 异步连接池实例.执行插入(
            插入语句, (我方微信号ID, 对方微信号ID, 新识别ID)
        )

        return {"识别id": 新识别ID, "是否新增": True}

    except Exception as e:
        错误日志器.error(f"异步添加微信好友并生成识别ID异常: {str(e)}")
        raise


async def 异步根据ID获取微信好友信息(我方微信号ID: int, 好友识别ID: int) -> dict:
    """
    根据我方微信号ID和好友识别ID获取微信好友信息

    参数:
        我方微信号ID: 我方微信号在数据库中的ID
        好友识别ID: 微信好友关系的识别ID（针对我方微信号唯一）

    返回:
        包含微信号信息的字典，如果未找到返回空字典
    """
    try:
        # 构建SQL查询，关联微信好友表和微信信息表
        查询语句 = """
            SELECT
                好友.我方微信号id,
                好友.对方微信号id,
                好友.创建时间,
                我方.微信号 AS 我方微信号,
                对方.微信号 AS 对方微信号
            FROM
                微信好友表 AS 好友
            JOIN
                微信信息表 AS 我方 ON 好友.我方微信号id = 我方.id
            JOIN
                微信信息表 AS 对方 ON 好友.对方微信号id = 对方.id
            WHERE
                好友.我方微信号id = $1 AND 好友.识别id = $2
        """

        # 执行查询
        结果 = await 异步连接池实例.执行查询(查询语句, (我方微信号ID, 好友识别ID))

        # 如果找到结果，返回第一条记录
        if 结果 and len(结果) > 0:
            return 结果[0]

        # 未找到结果，返回空字典
        return {}

    except Exception as e:
        错误日志器.error(f"异步根据ID获取微信好友信息异常: {str(e)}")
        raise





async def 异步获取推广用户列表(
    当前用户ID: int, 页码: int, 每页数量: int, 查询昵称或手机号: Optional[str] = None
) -> Dict[str, Any]:
    """
    异步获取指定用户ID推广的用户列表，支持分页和关键词搜索。

    参数:
        当前用户ID (int): 推荐人的用户ID.
        页码 (int): 当前页码.
        每页数量 (int): 每页显示的数量.
        查询昵称或手机号 (Optional[str]): 用于模糊搜索用户昵称或手机号的关键词.

    返回:
        Dict[str, Any]: 包含用户列表和总记录数的字典。
                         {'用户列表': 用户列表, 'total_count': 总数}
    """
    参数列表 = []
    用户查询条件 = "WHERE 邀请人 = %s"
    参数列表.append(当前用户ID)

    搜索条件 = ""
    if 查询昵称或手机号:
        搜索条件 = " AND (昵称 LIKE %s OR 手机号 LIKE %s)"
        参数列表.append(f"%{查询昵称或手机号}%")
        参数列表.append(f"%{查询昵称或手机号}%")

    # 查询总数
    总数查询SQL = f"SELECT COUNT(*) as total_count FROM 用户表 {用户查询条件}{搜索条件}"
    总数结果 = await 异步连接池实例.执行查询(总数查询SQL, tuple(参数列表))
    总记录数 = 总数结果[0]["total_count"] if 总数结果 else 0

    # 查询用户列表
    偏移量 = (页码 - 1) * 每页数量
    用户列表查询SQL = f"""
        SELECT id, 昵称, 手机号, created_at as registration_time, '' as avatar_url
        FROM 用户表
        {用户查询条件}{搜索条件}
        ORDER BY created_at DESC
        LIMIT $1 OFFSET $2
    """
    # 注意：LIMIT 和 OFFSET 的参数需要在参数列表的最后
    分页参数列表 = list(参数列表)  # 复制一份，避免修改原始参数列表
    分页参数列表.append(每页数量)
    分页参数列表.append(偏移量)

    用户列表 = await 异步连接池实例.执行查询(用户列表查询SQL, tuple(分页参数列表))

    # 处理datetime序列化问题
    处理后的用户列表 = []
    for 用户 in 用户列表 or []:
        处理后的用户 = dict(用户)
        # 将datetime对象转换为ISO格式字符串
        if 处理后的用户.get("registration_time"):
            处理后的用户["registration_time"] = 处理后的用户[
                "registration_time"
            ].isoformat()
        处理后的用户列表.append(处理后的用户)

    return {"用户列表": 处理后的用户列表, "total_count": 总记录数}


async def 异步更新用户最后登录时间(
    用户id: int, request: Optional[Request] = None
) -> bool:
    """异步更新用户最后登录时间，并记录到用户登陆记录表，包含IP归属地查询"""
    try:
        # {{ AURA-X: Modify - 简单直接的IP归属地查询，不搞复杂. Approval: 寸止(ID:1721062800). }}
        from 工具.IP归属地查询 import 查询IP归属地

        ip_address = "未知IP"  # 默认值
        if request:
            if (
                hasattr(request, "client")
                and request.client
                and hasattr(request.client, "host")
            ):
                ip_address = request.client.host
                数据库日志器.info(
                    f"从请求中获取到IP地址: {ip_address} (用户ID: {用户id})"
                )
            else:
                安全日志器.warning(
                    f"无法从 request.client.host 获取IP地址 (用户ID: {用户id})。Request client: {request.client if hasattr(request, 'client') else 'N/A'}"
                )
        else:
            安全日志器.warning(
                f"调用 异步更新用户最后登录时间 时未提供 request 对象 (用户ID: {用户id})，无法获取真实IP。"
            )

        # 查询IP归属地
        ip归属地 = 查询IP归属地(ip_address)
        数据库日志器.info(f"IP归属地查询结果: {ip_address} -> {ip归属地}")

        await 异步连接池实例.执行插入(
            "INSERT INTO 用户登陆记录表 (用户id, 登陆时间, IP地址, IP归属地) VALUES (%s, NOW(), %s, %s)",
            (用户id, ip_address, ip归属地),
        )
        数据库日志器.info(
            f"已为用户ID {用户id} 在 用户登陆记录表 中添加登录记录。IP: {ip_address}, 归属地: {ip归属地}"
        )
        return True
    except Exception as e:
        错误日志器.error(
            f"异步更新用户最后登录时间失败 (用户ID: {用户id}): {e}", exc_info=True
        )
        return False


async def 异步获取用户含邀约上限(用户id: int) -> Optional[dict]:
    """异步获取用户信息，确保包含每日免费邀约上限"""
    try:
        # 假设 用户表 中存在 每日免费邀约上限 字段
        # 如果字段名不同，请修改SQL查询
        sql = "SELECT id, 手机号, 昵称, 每日邀约次数 FROM 用户表 WHERE id = %s"
        结果 = await 异步连接池实例.执行查询(sql, (用户id,))
        if 结果:
            数据库日志器.debug(f"成功获取用户ID {用户id} 的邀约上限信息。")
            return 结果[0]
        数据库日志器.warning(f"尝试获取用户ID {用户id} 的邀约上限信息，但未找到用户。")
        return None
    except Exception as e:
        错误日志器.error(
            f"异步获取用户ID {用户id} 含邀约上限信息异常: {str(e)}", exc_info=True
        )
        return None


async def 异步获取用户昵称和邀约次数(用户id: int) -> Optional[dict]:
    """异步获取用户的昵称和每日邀约次数"""
    try:
        sql = "SELECT 昵称, 每日邀约次数 FROM 用户表 WHERE id = %s"
        结果 = await 异步连接池实例.执行查询(sql, (用户id,))
        if 结果:
            数据库日志器.debug(f"成功获取用户ID {用户id} 的昵称和邀约次数信息。")
            return 结果[0]
        数据库日志器.warning(
            f"尝试获取用户ID {用户id} 的昵称和邀约次数信息，但未找到用户。"
        )
        return None
    except Exception as e:
        错误日志器.error(
            f"异步获取用户ID {用户id} 的昵称和邀约次数信息异常: {str(e)}", exc_info=True
        )
        return None


async def 异步更新用户昵称(用户id: int, 新昵称: str) -> bool:
    """异步更新用户的昵称"""
    try:
        更新语句 = "UPDATE 用户表 SET 昵称 = %s WHERE id = %s"
        影响行数 = await 异步连接池实例.执行更新(更新语句, (新昵称, 用户id))
        if 影响行数 > 0:
            数据库日志器.info(f"成功更新用户ID {用户id} 的昵称为: {新昵称}")
            return True
        数据库日志器.warning(
            f"尝试更新用户ID {用户id} 的昵称，但可能用户不存在或昵称未改变。影响行数: {影响行数}"
        )
        return False  # 或者可以根据影响行数判断是否真的更新了，如果为0行可能是昵称与原来相同
    except Exception as e:
        错误日志器.error(f"异步更新用户ID {用户id} 的昵称异常: {str(e)}", exc_info=True)
        return False


async def 异步增加用户邀约次数(用户id: int, 增加数量: int) -> bool:
    """异步增加用户的每日邀约次数。如果原次数为NULL，则视为0再增加。"""
    try:
        # 使用 COALESCE 处理 NULL 值，确保其被视为 0
        更新语句 = "UPDATE 用户表 SET 每日邀约次数 = COALESCE(每日邀约次数, 0) + %s WHERE id = %s"
        影响行数 = await 异步连接池实例.执行更新(更新语句, (增加数量, 用户id))
        if 影响行数 > 0:
            数据库日志器.info(f"成功为用户ID {用户id} 增加 {增加数量} 次每日邀约次数。")
            return True
        数据库日志器.warning(
            f"尝试为用户ID {用户id} 增加邀约次数，但可能用户不存在。影响行数: {影响行数}"
        )
        return False
    except Exception as e:
        错误日志器.error(
            f"异步为用户ID {用户id} 增加邀约次数异常: {str(e)}", exc_info=True
        )
        return False


async def 异步获取用户今日成功邀约次数(用户id: int) -> int:
    """异步获取用户今日已成功（计次）的邀约次数"""
    try:
        # 修复查询条件：使用 = 1 替代 = TRUE，考虑时区问题
        sql = """
            SELECT COUNT(*) as count
            FROM 用户抖音达人邀约记录表
            WHERE 用户id = $1
              AND DATE(邀约发起时间) = CURRENT_DATE
              AND 当日计次 = 1
        """

        # 添加调试信息，帮助诊断时区问题
        调试sql = """
            SELECT CURRENT_DATE as today,
                   DATE(MAX(邀约发起时间)) as last_invitation_date,
                   COUNT(*) as total_invitations_today
            FROM 用户抖音达人邀约记录表
            WHERE 用户id = $1 AND 当日计次 = 1 AND DATE(邀约发起时间) = CURRENT_DATE
        """
        调试结果 = await 异步连接池实例.执行查询(调试sql, (用户id,))
        if 调试结果 and 调试结果[0]:
            数据库日志器.info(
                f"用户ID {用户id} 邀约统计调试信息: 今天={调试结果[0].get('today')}, 最后邀约日期={调试结果[0].get('last_invitation_date')}, 今日邀约数={调试结果[0].get('total_invitations_today')}"
            )

        结果 = await 异步连接池实例.执行查询(sql, (用户id,))
        if 结果 and 结果[0] and "count" in 结果[0]:
            次数 = int(结果[0]["count"])
            数据库日志器.info(f"用户ID {用户id} 今日已成功邀约 {次数} 次。")
            return 次数
        数据库日志器.warning(f"用户ID {用户id} 今日无成功邀约记录，或查询结果为空。")
        return 0
    except Exception as e:
        错误日志器.error(
            f"异步获取用户ID {用户id} 今日成功邀约次数异常: {str(e)}", exc_info=True
        )
        return 0  # 发生错误时，保守返回0，或者可以考虑抛出异常让上层处理


async def 异步记录邀约日志(
    用户id: int,
    kol_uid_number: str,
    kol_account_douyin: Optional[str],
    kol_nickname: Optional[str],
    邀约发起时间: datetime,  # 外部传入发起时间，确保与业务逻辑中的时间一致
    邀约状态: str,
    状态备注: Optional[str],
    当日计次: bool,
) -> int:
    """异步记录用户邀约KOL的日志，并返回日志ID。"""
    try:
        日志记录ID = await 异步连接池实例.执行插入(
            """INSERT INTO 用户抖音达人邀约记录表
                   (用户id, kol_uid_number, kol_account_douyin, kol_nickname, 邀约发起时间, 邀约状态, 状态备注, 当日计次)
               VALUES (%s, %s, %s, %s, %s, %s, %s, %s)""",
            (
                用户id,
                kol_uid_number,
                kol_account_douyin,
                kol_nickname,
                邀约发起时间,
                邀约状态,
                状态备注,
                当日计次,
            ),
        )
        数据库日志器.info(
            f"用户ID {用户id} 邀约KOL {kol_uid_number} 的日志已记录，日志ID: {日志记录ID}"
        )
        return 日志记录ID if 日志记录ID is not None else 0
    except Exception as e:
        错误日志器.error(
            f"记录邀约日志失败 (用户ID: {用户id}, KOL: {kol_uid_number}): {str(e)}",
            exc_info=True,
        )
        return 0  # 或者可以抛出异常，取决于上层如何处理


async def 异步更新用户邀请人(被推荐用户id: int, 推荐人id: int) -> bool:
    """异步更新指定用户的邀请人ID (邀请人 字段)"""
    try:
        更新结果 = await 异步连接池实例.执行更新(
            """UPDATE 用户表 SET 邀请人 = %s WHERE id = %s""", (推荐人id, 被推荐用户id)
        )
        # 执行更新通常返回影响的行数，如果大于0说明更新成功
        if 更新结果 > 0:
            数据库日志器.info(
                f"成功将用户ID {被推荐用户id} 的邀请人更新为用户ID {推荐人id}。"
            )
            return True
        else:
            # 如果行数为0，可能是用户ID不存在，或者"邀请人"已经是该值
            # 检查用户是否存在以提供更准确的日志
            用户是否存在 = await 异步用户是否存在_id(被推荐用户id)
            if not 用户是否存在:
                数据库日志器.warning(
                    f"尝试更新邀请人失败：用户ID {被推荐用户id} 不存在。"
                )
            else:
                数据库日志器.info(
                    f"更新用户ID {被推荐用户id} 的邀请人为 {推荐人id} 时，没有行受到影响（可能值已相同或用户不存在）。"
                )
            return False  # 或者根据业务需求返回True如果值已相同视为成功
    except Exception as e:
        错误日志器.error(
            f"异步更新用户ID {被推荐用户id} 的邀请人（为 {推荐人id}）时发生数据库异常: {str(e)}",
            exc_info=True,
        )
        return False


async def 异步更新用户邀请关联信息(
    被推荐用户id: int, 推荐人id: int, 代理类型表id: Optional[int] = None
) -> bool:
    """异步更新指定用户的邀请人ID（代理类型表id相关逻辑已移除）"""
    try:
        # 只更新邀请人字段，不再更新代理类型表id
        更新SQL = """UPDATE 用户表 SET 邀请人 = %s WHERE id = %s"""
        参数 = (推荐人id, 被推荐用户id)
        数据库日志器.info(f"准备更新用户ID {被推荐用户id} 的邀请人为 {推荐人id}")

        更新结果 = await 异步连接池实例.执行更新(更新SQL, 参数)

        # 执行更新通常返回影响的行数，如果大于0说明更新成功
        if 更新结果 > 0:
            数据库日志器.info(f"成功将用户ID {被推荐用户id} 的邀请人更新为 {推荐人id}")
            return True
        else:
            # 如果行数为0，可能是用户ID不存在，或者字段值已经相同
            用户是否存在 = await 异步用户是否存在_id(被推荐用户id)
            if not 用户是否存在:
                数据库日志器.warning(
                    f"尝试更新邀请关联信息失败：用户ID {被推荐用户id} 不存在。"
                )
            else:
                数据库日志器.info(
                    f"更新用户ID {被推荐用户id} 的邀请关联信息时，没有行受到影响（可能值已相同）。"
                )
            return False
    except Exception as e:
        错误日志器.error(
            f"异步更新用户ID {被推荐用户id} 的邀请关联信息时发生数据库异常: {str(e)}",
            exc_info=True,
        )
        return False


# 注意：请确保 异步连接池实例 和相关日志记录器已在此文件中正确定义和初始化
