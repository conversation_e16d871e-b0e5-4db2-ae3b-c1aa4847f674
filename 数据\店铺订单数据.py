"""
店铺订单数据层
负责店铺订单相关的数据库操作
"""

from typing import Any, Dict, List, Optional, Tuple

from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例


class 店铺订单数据层:
    """店铺订单数据访问层"""

    def __init__(self):
        self.数据库连接池 = 异步连接池实例

        # 安全字段白名单 - 防止SQL注入
        self.允许的字段名 = {
            # 基础订单字段
            "订单id",
            "商品id",
            "商品名称",
            "商品规格",
            "商品数量",
            "商品单价",
            "商品总价",
            "订单状态",
            "下单时间",
            "支付时间",
            "发货时间",
            "收货时间",
            "退款时间",
            "买家昵称",
            "买家手机",
            "收货地址",
            "物流公司",
            "物流单号",
            "备注",
            "抖音火山号",
            "用户订单认领表id",
            "创建时间",
            "更新时间",
            # 抖音电商字段
            "作者账号",
            "抖音/火山号",
            "支付金额",
            "佣金率",
            "预估佣金支出",
            "结算金额",
            "实际佣金支出",
            "超时未结算原因",
            "付款时间",
            "订单结算时间",
            "商品来源",
            "尾款支付时间",
            "定金金额",
            "店铺id",
            "店铺名称",
            "佣金发票",
            "冻结比例",
            "是否阶梯佣金",
            "门槛销量",
            "基础佣金率",
            "升佣佣金率",
            "预估奖励佣金支出",
            "结算奖励佣金支出",
            "阶梯计划ID",
            "支付补贴",
            "平台补贴",
            "达人补贴",
            "运费",
            "税费",
            "运费补贴",
            "分销来源",
            "营销活动id",
            "推广费率",
            "推广技术服务费",
            "预估推广费支出",
            "结算推广费支出",
            "计划类型",
            "订单来源",
            "流量细分来源",
            "流量来源",
            "订单类型",
        }

    def _验证字段名安全性(self, 字段名: str) -> bool:
        """
        验证字段名是否安全（防止SQL注入）

        参数:
            字段名: 要验证的字段名

        返回:
            bool: 字段名是否安全
        """
        # 检查是否在白名单中
        if 字段名 not in self.允许的字段名:
            from 日志 import 数据库日志器

            数据库日志器.error(f"检测到不安全的字段名: {字段名}")
            return False

        # 检查是否包含危险字符
        危险字符 = [
            ";",
            "--",
            "/*",
            "*/",
            "xp_",
            "sp_",
            "exec",
            "execute",
            "drop",
            "delete",
            "update",
            "insert",
        ]
        字段名_小写 = 字段名.lower()
        for 危险字符 in 危险字符:
            if 危险字符 in 字段名_小写:
                from 日志 import 数据库日志器

                数据库日志器.error(f"字段名包含危险字符: {字段名}")
                return False

        return True

    async def 检查订单是否存在(self, 订单id: int) -> bool:
        """
        检查订单是否已存在

        参数:
            订单id: 订单ID

        返回:
            bool: 订单是否存在
        """
        try:
            检查查询 = "SELECT COUNT(*) as count FROM 店铺订单表 WHERE 订单id = %s"
            检查结果 = await self.数据库连接池.执行查询(检查查询, (订单id,))

            return 检查结果 and 检查结果[0]["count"] > 0

        except Exception as e:
            from 日志 import 数据库日志器

            数据库日志器.error(f"检查订单是否存在失败: {str(e)}")
            return False

    async def 插入订单数据(self, 订单数据: Dict[str, Any]) -> bool:
        """
        插入订单数据到数据库

        参数:
            订单数据: 订单数据字典

        返回:
            bool: 插入是否成功
        """
        try:
            # 安全验证：检查所有字段名
            字段列表 = list(订单数据.keys())
            for 字段名 in 字段列表:
                if not self._验证字段名安全性(字段名):
                    from 日志 import 数据库日志器

                    数据库日志器.error(f"插入订单数据失败: 不安全的字段名 {字段名}")
                    return False

            # 构建安全的插入SQL
            占位符 = ", ".join(["%s"] * len(字段列表))
            字段名称 = ", ".join([f"`{字段}`" for 字段 in 字段列表])

            插入查询 = f"""
            INSERT INTO 店铺订单表 ({字段名称})
            VALUES ({占位符})
            """

            参数值 = tuple(订单数据.values())

            # 执行插入
            await self.数据库连接池.执行插入(插入查询, 参数值)
            from 日志 import 数据库日志器

            数据库日志器.debug(f"成功插入订单数据: 订单ID={订单数据.get('订单id')}")
            return True

        except Exception as e:
            from 日志 import 数据库日志器

            数据库日志器.error(f"插入订单数据失败: {str(e)}")
            return False

    async def 获取用户店铺权限(self, 用户ID: int) -> List[int]:
        """
        获取用户有权限的店铺ID列表

        参数:
            用户ID: 用户ID

        返回:
            List[int]: 店铺ID列表
        """
        try:
            权限查询 = """
            SELECT 店铺ID FROM 用户_店铺 
            WHERE 用户ID = %s
            """

            权限结果 = await self.数据库连接池.执行查询(权限查询, (用户ID,))

            return [row["店铺ID"] for row in 权限结果] if 权限结果 else []

        except Exception as e:
            from 日志 import 数据库日志器

            数据库日志器.error(f"获取用户店铺权限失败: {str(e)}")
            return []

    async def 查询订单列表(
        self,
        用户ID: int,
        页码: int = 1,
        每页数量: int = 20,
        店铺ID: Optional[int] = None,
        订单状态: Optional[str] = None,
        商品名称: Optional[str] = None,
        开始时间: Optional[str] = None,
        结束时间: Optional[str] = None,
    ) -> Tuple[List[Dict[str, Any]], int]:
        """
        查询订单列表

        参数:
            用户ID: 用户ID
            页码: 页码
            每页数量: 每页数量
            店铺ID: 店铺ID筛选
            订单状态: 订单状态筛选
            开始时间: 开始时间筛选
            结束时间: 结束时间筛选

        返回:
            Tuple[List[Dict], int]: (订单列表, 总数)
        """
        try:
            # 构建查询条件
            查询条件 = []
            查询参数 = []

            # 基础查询：只查询用户有权限的店铺订单
            基础查询 = """
            SELECT 
                o.订单id,
                o.商品id,
                o.商品名称,
                o.作者账号,
                o.支付金额,
                o.佣金率,
                o.预估佣金支出,
                o.实际佣金支出,
                o.订单状态,
                o.付款时间,
                o.收货时间,
                o.店铺id,
                o.店铺名称,
                o.商品数量,
                o.创建时间
            FROM 店铺订单表 o
            WHERE EXISTS (
                SELECT 1 FROM 用户_店铺 us
                INNER JOIN 店铺 s ON us.店铺ID = s.id
                WHERE us.用户ID = %s
                AND s.shop_id COLLATE utf8mb4_unicode_ci = o.店铺id COLLATE utf8mb4_unicode_ci
            )
            """
            查询参数.append(用户ID)

            # 添加筛选条件
            if 店铺ID:
                查询条件.append("o.店铺id = %s")
                查询参数.append(str(店铺ID))

            if 订单状态:
                查询条件.append("o.订单状态 = %s")
                查询参数.append(订单状态)

            if 商品名称:
                查询条件.append("o.商品名称 LIKE %s")
                查询参数.append(f"%{商品名称}%")

            if 开始时间:
                查询条件.append("o.付款时间 >= %s")
                查询参数.append(开始时间)

            if 结束时间:
                查询条件.append("o.付款时间 <= %s")
                查询参数.append(结束时间)

            # 组装完整查询
            if 查询条件:
                完整查询 = 基础查询 + " AND " + " AND ".join(查询条件)
            else:
                完整查询 = 基础查询

            # 添加排序
            完整查询 += " ORDER BY o.创建时间 DESC"

            # 计算总数
            计数查询 = f"SELECT COUNT(*) as total FROM ({完整查询}) as temp"
            总数结果 = await self.数据库连接池.执行查询(计数查询, tuple(查询参数))
            总数 = 总数结果[0]["total"] if 总数结果 else 0

            # 分页查询
            偏移量 = (页码 - 1) * 每页数量
            分页查询 = 完整查询 + f" LIMIT {每页数量} OFFSET {偏移量}"

            订单列表 = await self.数据库连接池.执行查询(分页查询, tuple(查询参数))

            from 日志 import 数据库日志器

            数据库日志器.debug(f"查询订单列表成功: 用户ID={用户ID}, 总数={总数}")
            return 订单列表 or [], 总数

        except Exception as e:
            from 日志 import 数据库日志器

            数据库日志器.error(f"查询订单列表失败: {str(e)}")
            return [], 0

    async def 获取订单详情(self, 订单id: int, 用户ID: int) -> Optional[Dict[str, Any]]:
        """
        获取订单详情

        参数:
            订单id: 订单ID
            用户ID: 用户ID

        返回:
            Optional[Dict]: 订单详情
        """
        try:
            详情查询 = """
            SELECT o.*
            FROM 店铺订单表 o
            WHERE o.订单id = %s
            AND EXISTS (
                SELECT 1 FROM 用户_店铺 us 
                WHERE us.用户ID = %s 
                AND (us.店铺ID = CAST(o.店铺id AS UNSIGNED) OR o.店铺id IS NULL)
            )
            """

            详情结果 = await self.数据库连接池.执行查询(
                详情查询, (订单id, 用户ID)
            )

            if 详情结果:
                from 日志 import 数据库日志器

                数据库日志器.debug(f"获取订单详情成功: 订单ID={订单id}")
                return 详情结果[0]
            else:
                from 日志 import 数据库日志器

                数据库日志器.warning(
                    f"订单不存在或无权限访问: 订单ID={订单id}, 用户ID={用户ID}"
                )
                return None

        except Exception as e:
            from 日志 import 数据库日志器

            数据库日志器.error(f"获取订单详情失败: {str(e)}")
            return None

    async def 获取订单状态选项(self, 用户ID: int) -> List[str]:
        """
        获取订单状态选项

        参数:
            用户ID: 用户ID

        返回:
            List[str]: 订单状态选项列表
        """
        try:
            状态查询 = """
            SELECT DISTINCT o.订单状态
            FROM 店铺订单表 o
            WHERE EXISTS (
                SELECT 1 FROM 用户_店铺 us
                INNER JOIN 店铺 s ON us.店铺ID = s.id
                WHERE us.用户ID = %s
                AND s.shop_id COLLATE utf8mb4_unicode_ci = o.店铺id COLLATE utf8mb4_unicode_ci
            )
            AND o.订单状态 IS NOT NULL
            AND o.订单状态 != ''
            ORDER BY o.订单状态
            """

            状态结果 = await self.数据库连接池.执行查询(状态查询, (用户ID,))

            状态列表 = [row["订单状态"] for row in 状态结果] if 状态结果 else []

            from 日志 import 数据库日志器

            数据库日志器.debug(
                f"获取订单状态选项成功: 用户ID={用户ID}, 状态数量={len(状态列表)}"
            )
            return 状态列表

        except Exception as e:
            from 日志 import 数据库日志器

            数据库日志器.error(f"获取订单状态选项失败: {str(e)}")
            return []

    async def 查询订单详情(self, 订单id: str, 用户ID: int) -> Optional[Dict[str, Any]]:
        """
        查询单个订单的详细信息

        参数:
            订单id: 订单ID（字符串格式，避免大数精度问题）
            用户ID: 用户ID（用于权限验证）

        返回:
            Optional[Dict[str, Any]]: 订单详情，包含关联的达人信息
        """
        try:
            详情查询 = """
            SELECT
                o.*,
                CASE
                    WHEN uc.id IS NOT NULL THEN uc.抖音号
                    ELSE NULL
                END as 关联抖音号,
                CASE
                    WHEN uc.id IS NOT NULL THEN uc.达人表id
                    ELSE NULL
                END as 关联达人表id
            FROM 店铺订单表 o
            LEFT JOIN 用户订单认领表 uc ON o.用户订单认领表id = uc.id
            WHERE CAST(o.订单id AS CHAR) = %s
            AND EXISTS (
                SELECT 1 FROM 用户_店铺 us
                INNER JOIN 店铺 s ON us.店铺ID = s.id
                WHERE us.用户ID = %s
                AND s.shop_id COLLATE utf8mb4_unicode_ci = o.店铺id COLLATE utf8mb4_unicode_ci
            )
            """

            详情结果 = await self.数据库连接池.执行查询(
                详情查询, (订单id, 用户ID)
            )

            if 详情结果:
                订单详情 = 详情结果[0]
                from 日志 import 数据库日志器

                数据库日志器.debug(
                    f"查询订单详情成功: 订单ID={订单id}, 用户ID={用户ID}"
                )
                return 订单详情
            else:
                from 日志 import 数据库日志器

                数据库日志器.warning(
                    f"订单不存在或无权限访问: 订单ID={订单id}, 用户ID={用户ID}"
                )
                return None

        except Exception as e:
            from 日志 import 数据库日志器

            数据库日志器.error(
                f"查询订单详情失败: 订单ID={订单id}, 用户ID={用户ID}, 错误={str(e)}"
            )
            return None

    async def 查找达人信息(self, 抖音火山号: str) -> Optional[int]:
        """
        在kol数据库中查找达人信息

        参数:
            抖音火山号: 抖音火山号

        返回:
            Optional[int]: 达人表id，如果找不到返回None
        """
        try:
            查找查询 = """
            SELECT id FROM kol.达人表
            WHERE account_douyin = %s
            LIMIT 1
            """

            查找结果 = await self.数据库连接池.执行查询(查找查询, (抖音火山号,))

            if 查找结果:
                达人id = 查找结果[0]["id"]
                from 日志 import 数据库日志器

                数据库日志器.debug(
                    f"找到达人信息: 抖音火山号={抖音火山号}, 达人id={达人id}"
                )
                return 达人id
            else:
                from 日志 import 数据库日志器

                数据库日志器.debug(f"未找到达人信息: 抖音火山号={抖音火山号}")
                return None

        except Exception as e:
            from 日志 import 数据库日志器

            数据库日志器.error(f"查找达人信息失败: {str(e)}")
            return None

    async def 检查用户订单认领记录存在(
        self, 抖音号: str, 达人表id: int
    ) -> Optional[int]:
        """
        检查用户订单认领记录是否已存在

        参数:
            抖音号: 抖音号
            达人表id: 达人表id

        返回:
            Optional[int]: 如果存在返回记录id，不存在返回None
        """
        try:
            检查查询 = """
            SELECT id FROM 用户订单认领表
            WHERE 抖音号 = %s AND 达人表id = %s
            LIMIT 1
            """

            检查结果 = await self.数据库连接池.执行查询(
                检查查询, (抖音号, 达人表id)
            )

            if 检查结果:
                记录id = 检查结果[0]["id"]
                from 日志 import 数据库日志器

                数据库日志器.debug(
                    f"找到现有用户订单认领记录: 抖音号={抖音号}, 达人表id={达人表id}, 记录id={记录id}"
                )
                return 记录id
            else:
                return None

        except Exception as e:
            from 日志 import 数据库日志器

            数据库日志器.error(f"检查用户订单认领记录失败: {str(e)}")
            return None

    async def 检查用户订单认领记录存在_仅抖音号(self, 抖音号: str) -> Optional[int]:
        """
        仅基于抖音号检查用户订单认领记录是否已存在

        参数:
            抖音号: 抖音号

        返回:
            Optional[int]: 如果存在返回记录id，不存在返回None
        """
        try:
            检查查询 = """
            SELECT id FROM 用户订单认领表
            WHERE 抖音号 = %s
            LIMIT 1
            """

            检查结果 = await self.数据库连接池.执行查询(检查查询, (抖音号,))

            if 检查结果:
                记录id = 检查结果[0]["id"]
                from 日志 import 数据库日志器

                数据库日志器.debug(
                    f"找到现有用户订单认领记录（仅抖音号）: 抖音号={抖音号}, 记录id={记录id}"
                )
                return 记录id
            else:
                return None

        except Exception as e:
            from 日志 import 数据库日志器

            数据库日志器.error(f"检查用户订单认领记录失败（仅抖音号）: {str(e)}")
            return None

    async def 插入用户订单认领记录(self, 抖音号: str, 达人表id: int) -> Optional[int]:
        """
        插入用户订单认领表记录（带重复性检查）

        参数:
            抖音号: 抖音号
            达人表id: 达人表id

        返回:
            Optional[int]: 记录id（现有或新插入），失败返回None
        """
        try:
            # 先检查是否已存在
            现有记录id = await self.检查用户订单认领记录存在(抖音号, 达人表id)
            if 现有记录id:
                from 日志 import 数据库日志器

                数据库日志器.debug(f"使用现有用户订单认领记录: id={现有记录id}")
                return 现有记录id

            # 不存在则插入新记录
            插入查询 = """
            INSERT INTO 用户订单认领表 (抖音号, 达人表id)
            VALUES (%s, %s)
            """

            await self.数据库连接池.执行插入(插入查询, (抖音号, 达人表id))

            # 获取插入的记录id
            获取id查询 = "SELECT LAST_INSERT_ID() as id"
            id结果 = await self.数据库连接池.执行查询(获取id查询)

            if id结果:
                认领记录id = id结果[0]["id"]
                from 日志 import 数据库日志器

                数据库日志器.debug(
                    f"插入新用户订单认领记录成功: 抖音号={抖音号}, 达人表id={达人表id}, 认领记录id={认领记录id}"
                )
                return 认领记录id
            else:
                return None

        except Exception as e:
            from 日志 import 数据库日志器

            数据库日志器.error(f"插入用户订单认领记录失败: {str(e)}")
            return None

    async def 创建用户订单认领记录(self, 抖音号: str, 达人表id: int) -> Optional[int]:
        """
        创建用户订单认领表记录（仅插入新记录，不检查重复）

        参数:
            抖音号: 抖音号
            达人表id: 达人表id

        返回:
            Optional[int]: 新记录id，失败返回None
        """
        try:
            from 日志 import 数据库日志器

            # 使用事务确保数据一致性
            async with self.数据库连接池.获取连接() as 连接:
                async with 连接.cursor() as 游标:
                    # 插入新记录
                    插入查询 = """
                    INSERT INTO 用户订单认领表 (抖音号, 达人表id)
                    VALUES (%s, %s)
                    """

                    await 游标.execute(插入查询, (抖音号, 达人表id))

                    # 获取刚插入的记录ID
                    认领记录id = 游标.lastrowid

                    # 提交事务
                    await 连接.commit()

                    数据库日志器.debug(
                        f"创建新用户订单认领记录成功: 抖音号={抖音号}, 达人表id={达人表id}, 认领记录id={认领记录id}"
                    )
                    return 认领记录id

        except Exception as e:
            from 日志 import 数据库日志器

            数据库日志器.error(f"创建用户订单认领记录失败: {str(e)}")
            return None

    async def 创建用户订单认领记录_仅抖音号(self, 抖音号: str) -> Optional[int]:
        """
        创建用户订单认领表记录（仅抖音号，达人表id为NULL）

        参数:
            抖音号: 抖音号

        返回:
            Optional[int]: 新记录id，失败返回None
        """
        try:
            from 日志 import 数据库日志器

            # 使用事务确保数据一致性
            async with self.数据库连接池.获取连接() as 连接:
                async with 连接.cursor() as 游标:
                    # 插入新记录（达人表id为NULL）
                    插入查询 = """
                    INSERT INTO 用户订单认领表 (抖音号, 达人表id)
                    VALUES (%s, NULL)
                    """

                    await 游标.execute(插入查询, (抖音号,))

                    # 获取刚插入的记录ID
                    认领记录id = 游标.lastrowid

                    # 提交事务
                    await 连接.commit()

                    数据库日志器.debug(
                        f"创建新用户订单认领记录成功（仅抖音号）: 抖音号={抖音号}, 认领记录id={认领记录id}"
                    )
                    return 认领记录id

        except Exception as e:
            from 日志 import 数据库日志器

            数据库日志器.error(f"创建用户订单认领记录失败（仅抖音号）: {str(e)}")
            return None

    async def 更新订单认领关联(self, 订单id: int, 用户订单认领表id: int) -> bool:
        """
        更新店铺订单表的用户订单认领表id字段

        参数:
            订单id: 订单id
            用户订单认领表id: 用户订单认领表id

        返回:
            bool: 更新是否成功
        """
        try:
            更新查询 = """
            UPDATE 店铺订单表
            SET 用户订单认领表id = %s
            WHERE 订单id = %s
            """

            await self.数据库连接池.执行更新(更新查询, (用户订单认领表id, 订单id))

            from 日志 import 数据库日志器

            数据库日志器.debug(
                f"更新订单认领关联成功: 订单id={订单id}, 用户订单认领表id={用户订单认领表id}"
            )
            return True

        except Exception as e:
            from 日志 import 数据库日志器

            数据库日志器.error(f"更新订单认领关联失败: {str(e)}")
            return False

    async def 更新用户订单认领记录_达人表id(
        self, 认领记录id: int, 达人表id: int
    ) -> bool:
        """
        更新用户订单认领表记录的达人表id字段

        参数:
            认领记录id: 认领记录id
            达人表id: 达人表id

        返回:
            bool: 更新是否成功
        """
        try:
            更新查询 = """
            UPDATE 用户订单认领表
            SET 达人表id = %s
            WHERE id = %s
            """

            await self.数据库连接池.执行更新(更新查询, (达人表id, 认领记录id))

            from 日志 import 数据库日志器

            数据库日志器.debug(
                f"更新用户订单认领记录达人表id成功: 认领记录id={认领记录id}, 达人表id={达人表id}"
            )
            return True

        except Exception as e:
            from 日志 import 数据库日志器

            数据库日志器.error(f"更新用户订单认领记录达人表id失败: {str(e)}")
            return False

    async def 验证订单认领关联(self, 订单id: int) -> Dict[str, Any]:
        """
        验证订单认领关联的正确性（用于调试）

        参数:
            订单id: 订单ID

        返回:
            Dict: 验证结果
        """
        try:
            验证查询 = """
            SELECT
                o.订单id,
                o.抖音火山号 as 订单_抖音火山号,
                o.用户订单认领表id,
                u.抖音号 as 认领表_抖音号,
                u.达人表id as 认领表_达人表id
            FROM 店铺订单表 o
            LEFT JOIN 用户订单认领表 u ON o.用户订单认领表id = u.id
            WHERE o.订单id = %s
            """

            验证结果 = await self.数据库连接池.执行查询(验证查询, (订单id,))

            if 验证结果:
                结果 = 验证结果[0]
                是否匹配 = 结果["订单_抖音火山号"] == 结果["认领表_抖音号"]

                return {
                    "订单id": 结果["订单id"],
                    "订单_抖音火山号": 结果["订单_抖音火山号"],
                    "用户订单认领表id": 结果["用户订单认领表id"],
                    "认领表_抖音号": 结果["认领表_抖音号"],
                    "认领表_达人表id": 结果["认领表_达人表id"],
                    "关联是否正确": 是否匹配,
                }
            else:
                return {"错误": "订单不存在"}

        except Exception as e:
            from 日志 import 数据库日志器

            数据库日志器.error(f"验证订单认领关联失败: {str(e)}")
            return {"错误": str(e)}

    async def 检查用户文件重复导入(
        self, 用户ID: int, 文件hash: str
    ) -> Optional[Dict[str, Any]]:
        """
        基于文件hash检查用户是否已经导入过相同的文件

        参数:
            用户ID: 用户ID
            文件hash: 文件MD5哈希值

        返回:
            Optional[Dict]: 如果找到重复文件，返回现有记录信息
        """
        try:
            查询SQL = """
            SELECT 任务ID, id, 任务状态, 进度百分比, 开始时间, 文件路径, 文件名
            FROM 店铺订单_导入记录表
            WHERE 用户ID = $1 AND 文件hash = $2
            AND 开始时间 >= CURRENT_TIMESTAMP - INTERVAL '24 hours'
            AND 任务状态 IN ('进行中', '超时', '已完成', '部分失败')
            ORDER BY 开始时间 DESC
            LIMIT 1
            """

            结果 = await self.数据库连接池.执行查询(查询SQL, (用户ID, 文件hash))

            if 结果:
                记录 = 结果[0]
                from 日志 import 数据库日志器

                数据库日志器.info(
                    f"数据库中发现重复文件: 用户{用户ID}, hash={文件hash[:8]}, 现有任务={记录['任务ID']}"
                )
                return 记录

            return None

        except Exception as e:
            from 日志 import 数据库日志器

            数据库日志器.error(f"检查文件重复导入失败: {str(e)}")
            return None

    async def 创建导入记录(
        self,
        任务ID: str,
        用户ID: int,
        文件名: str,
        文件大小: int,
        文件路径: str = None,
        文件hash: str = None,
    ) -> Optional[int]:
        """
        创建导入记录

        参数:
            任务ID: 唯一任务标识
            用户ID: 用户ID
            文件名: 文件名
            文件大小: 文件大小（字节）
            文件路径: Excel文件存储路径（可选）

        返回:
            Optional[int]: 导入记录ID，失败返回None
        """
        try:
            插入查询 = """
            INSERT INTO 店铺订单_导入记录表 (
                任务ID, 用户ID, 文件名, 文件大小, 文件路径, 文件hash, 当前批次, 已处理行数,
                可续传, 任务状态, 进度百分比
            ) VALUES (%s, %s, %s, %s, %s, %s, 0, 0, %s, '进行中', 0.00)
            """

            可续传 = 1 if 文件路径 else 0
            await self.数据库连接池.执行插入(
                插入查询, (任务ID, 用户ID, 文件名, 文件大小, 文件路径, 文件hash, 可续传)
            )

            # 通过任务ID查询获取记录ID（更可靠的方式）
            获取id查询 = "SELECT id FROM 店铺订单_导入记录表 WHERE 任务ID = %s"
            id结果 = await self.数据库连接池.执行查询(获取id查询, (任务ID,))

            if id结果:
                导入记录ID = id结果[0]["id"]
                from 日志 import 数据库日志器

                数据库日志器.info(
                    f"创建导入记录成功: 任务ID={任务ID}, 记录ID={导入记录ID}"
                )
                return 导入记录ID
            else:
                from 日志 import 数据库日志器

                数据库日志器.error(f"创建导入记录后无法获取ID: 任务ID={任务ID}")
                return None

        except Exception as e:
            from 日志 import 数据库日志器

            数据库日志器.error(f"创建导入记录失败: {str(e)}")
            return None

    async def 更新导入记录状态(
        self, 导入记录ID: int, 状态: str, 进度百分比: float, 错误信息: str = None
    ):
        """
        更新导入记录状态
        """
        try:
            if 错误信息:
                更新查询 = """
                UPDATE 店铺订单_导入记录表
                SET 任务状态 = %s, 进度百分比 = %s, 错误信息 = %s, 更新时间 = NOW()
                WHERE id = %s
                """
                await self.数据库连接池.执行更新(
                    更新查询, (状态, 进度百分比, 错误信息, 导入记录ID)
                )
            else:
                更新查询 = """
                UPDATE 店铺订单_导入记录表
                SET 任务状态 = %s, 进度百分比 = %s, 更新时间 = NOW()
                WHERE id = %s
                """
                await self.数据库连接池.执行更新(
                    更新查询, (状态, 进度百分比, 导入记录ID)
                )

        except Exception as e:
            from 日志 import 数据库日志器

            数据库日志器.error(f"更新导入记录状态失败: {str(e)}")

    async def 更新导入记录总行数(self, 导入记录ID: int, 总行数: int):
        """
        更新导入记录总行数
        """
        try:
            更新查询 = """
            UPDATE 店铺订单_导入记录表
            SET 总行数 = %s, 更新时间 = NOW()
            WHERE id = %s
            """
            await self.数据库连接池.执行更新(更新查询, (总行数, 导入记录ID))

        except Exception as e:
            from 日志 import 数据库日志器

            数据库日志器.error(f"更新导入记录总行数失败: {str(e)}")

    async def 更新导入记录进度(
        self,
        导入记录ID: int,
        成功数量: int,
        失败数量: int,
        跳过数量: int,
        进度百分比: float,
    ):
        """
        更新导入记录进度 - 增强调试日志
        """
        try:
            from 日志 import 数据库日志器

            数据库日志器.info(
                f"[进度更新] 开始更新: ID={导入记录ID}, 成功={成功数量}, 失败={失败数量}, 跳过={跳过数量}, 进度={进度百分比:.2f}%"
            )

            更新查询 = """
            UPDATE 店铺订单_导入记录表
            SET 成功数量 = %s, 失败数量 = %s, 跳过数量 = %s, 进度百分比 = %s, 更新时间 = NOW()
            WHERE id = %s
            """
            影响行数 = await self.数据库连接池.执行更新(
                更新查询, (成功数量, 失败数量, 跳过数量, 进度百分比, 导入记录ID)
            )

            数据库日志器.info(
                f"[进度更新] 更新成功: ID={导入记录ID}, 影响行数={影响行数}"
            )

        except Exception as e:
            from 日志 import 数据库日志器, 错误日志器

            错误日志器.error(f"[进度更新] 更新失败: ID={导入记录ID}, 错误={str(e)}")
            错误日志器.error(
                f"[进度更新] 异常类型: {type(e).__name__}, 详情: {repr(e)}"
            )
            # 重新抛出异常让上层知道
            raise

    async def 更新批次进度(
        self,
        导入记录ID: int,
        当前批次: int,
        已处理行数: int,
        成功数量: int,
        失败数量: int,
        跳过数量: int,
        进度百分比: float,
    ):
        """
        更新批次处理进度
        """
        try:
            from 日志 import 数据库日志器

            数据库日志器.info(
                f"[批次更新] ID={导入记录ID}, 批次={当前批次}, 已处理={已处理行数}, 成功={成功数量}, 失败={失败数量}, 跳过={跳过数量}"
            )

            更新查询 = """
            UPDATE 店铺订单_导入记录表
            SET 当前批次 = %s, 已处理行数 = %s, 成功数量 = %s, 失败数量 = %s,
                跳过数量 = %s, 进度百分比 = %s, 更新时间 = NOW()
            WHERE id = %s
            """
            await self.数据库连接池.执行更新(
                更新查询,
                (
                    当前批次,
                    已处理行数,
                    成功数量,
                    失败数量,
                    跳过数量,
                    进度百分比,
                    导入记录ID,
                ),
            )

        except Exception as e:
            from 日志 import 错误日志器

            错误日志器.error(f"更新批次进度失败: {str(e)}")
            raise

    async def 完成导入记录(
        self,
        导入记录ID: int,
        最终状态: str,
        成功数量: int,
        失败数量: int,
        跳过数量: int,
        进度百分比: float,
        错误信息: str = None,
    ):
        """
        完成导入记录 - 增强调试日志
        """
        try:
            from 日志 import 数据库日志器

            数据库日志器.info(
                f"[完成记录] 开始更新: ID={导入记录ID}, 状态={最终状态}, 成功={成功数量}, 失败={失败数量}, 跳过={跳过数量}"
            )

            更新查询 = """
            UPDATE 店铺订单_导入记录表
            SET 任务状态 = %s, 成功数量 = %s, 失败数量 = %s, 跳过数量 = %s,
                进度百分比 = %s, 错误信息 = %s, 完成时间 = NOW(), 更新时间 = NOW()
            WHERE id = %s
            """
            影响行数 = await self.数据库连接池.执行更新(
                更新查询,
                (
                    最终状态,
                    成功数量,
                    失败数量,
                    跳过数量,
                    进度百分比,
                    错误信息,
                    导入记录ID,
                ),
            )

            数据库日志器.info(
                f"[完成记录] 更新成功: ID={导入记录ID}, 影响行数={影响行数}"
            )

        except Exception as e:
            from 日志 import 数据库日志器, 错误日志器

            错误日志器.error(f"[完成记录] 更新失败: ID={导入记录ID}, 错误={str(e)}")
            错误日志器.error(
                f"[完成记录] 异常类型: {type(e).__name__}, 详情: {repr(e)}"
            )
            # 重新抛出异常让上层知道
            raise

    async def 批量检查订单存在(self, 订单ids: list) -> set:
        """
        批量检查订单是否存在

        参数:
            订单ids: 订单ID列表

        返回:
            set: 已存在的订单ID集合
        """
        try:
            if not 订单ids:
                return set()

            # 构建IN查询
            占位符 = ",".join(["%s"] * len(订单ids))
            查询语句 = f"""
            SELECT 订单id FROM 店铺订单表
            WHERE 订单id IN ({占位符})
            """

            结果 = await self.数据库连接池.执行查询(查询语句, tuple(订单ids))

            已存在订单集合 = {row["订单id"] for row in 结果} if 结果 else set()

            from 日志 import 数据库日志器

            数据库日志器.debug(
                f"批量检查订单存在: 检查{len(订单ids)}个，已存在{len(已存在订单集合)}个"
            )

            return 已存在订单集合

        except Exception as e:
            from 日志 import 数据库日志器

            数据库日志器.error(f"批量检查订单存在失败: {str(e)}")
            return set()

    async def 查询导入记录(self, 任务ID: str, 用户ID: int) -> Optional[Dict[str, Any]]:
        """
        查询导入记录
        """
        try:
            查询SQL = """
            SELECT * FROM 店铺订单_导入记录表
            WHERE 任务ID = %s AND 用户ID = %s
            """

            结果 = await self.数据库连接池.执行查询(查询SQL, (任务ID, 用户ID))

            if 结果:
                return 结果[0]
            else:
                return None

        except Exception as e:
            from 日志 import 数据库日志器

            数据库日志器.error(f"查询导入记录失败: {str(e)}")
            return None

    async def 查询导入记录_通过ID(
        self, 导入记录ID: int, 用户ID: int
    ) -> Optional[Dict[str, Any]]:
        """
        通过ID查询导入记录
        """
        try:
            查询SQL = """
            SELECT * FROM 店铺订单_导入记录表
            WHERE id = %s AND 用户ID = %s
            """

            结果 = await self.数据库连接池.执行查询(查询SQL, (导入记录ID, 用户ID))

            if 结果:
                return 结果[0]
            return None

        except Exception as e:
            from 日志 import 数据库日志器

            数据库日志器.error(f"查询导入记录失败: {str(e)}")
            return None

    async def 查询导入记录列表(
        self, 用户ID: int, 页码: int = 1, 每页数量: int = 20, 状态筛选: str = None
    ) -> Tuple[List[Dict[str, Any]], int]:
        """
        查询用户的导入记录列表
        """
        try:
            查询条件 = ["用户ID = %s"]
            查询参数 = [用户ID]

            if 状态筛选:
                查询条件.append("任务状态 = %s")
                查询参数.append(状态筛选)

            基础查询 = (
                """
            SELECT id, 任务ID, 文件名, 任务状态, 总行数, 成功数量, 失败数量, 跳过数量,
                   进度百分比, 开始时间, 完成时间, 错误信息, 可续传, 已处理行数, 当前批次
            FROM 店铺订单_导入记录表
            WHERE """
                + " AND ".join(查询条件)
                + """
            ORDER BY 开始时间 DESC
            """
            )

            # 计算总数
            计数查询 = f"SELECT COUNT(*) as total FROM 店铺订单_导入记录表 WHERE {' AND '.join(查询条件)}"
            总数结果 = await self.数据库连接池.执行查询(计数查询, tuple(查询参数))
            总数 = 总数结果[0]["total"] if 总数结果 else 0

            # 分页查询
            偏移量 = (页码 - 1) * 每页数量
            分页查询 = 基础查询 + f" LIMIT {每页数量} OFFSET {偏移量}"

            记录列表 = await self.数据库连接池.执行查询(分页查询, tuple(查询参数))

            from 日志 import 数据库日志器

            数据库日志器.debug(f"查询导入记录列表成功: 用户ID={用户ID}, 总数={总数}")
            return 记录列表 or [], 总数

        except Exception as e:
            from 日志 import 数据库日志器

            数据库日志器.error(f"查询导入记录列表失败: {str(e)}")
            return [], 0

    async def 批量插入订单数据(self, 订单数据列表: list) -> int:
        """
        批量插入订单数据 - 性能优化（修复版）

        参数:
            订单数据列表: 要插入的订单数据列表

        返回:
            int: 成功插入的数量
        """
        if not 订单数据列表:
            return 0

        try:
            from 日志 import 数据库日志器

            # 使用第一条数据的字段来动态构建插入查询
            第一条数据 = 订单数据列表[0]
            字段列表 = list(第一条数据.keys())

            # 安全验证：检查所有字段名
            for 字段名 in 字段列表:
                if not self._验证字段名安全性(字段名):
                    数据库日志器.error(f"批量插入订单数据失败: 不安全的字段名 {字段名}")
                    return 0

            # 构建安全的动态插入查询
            字段名称 = ", ".join([f"`{字段}`" for 字段 in 字段列表])
            占位符 = ", ".join(["%s"] * len(字段列表))

            插入查询 = f"""
            INSERT INTO 店铺订单表 ({字段名称})
            VALUES ({占位符})
            """

            # 准备批量插入的数据
            插入数据 = []
            for 数据 in 订单数据列表:
                # 按照字段列表的顺序提取数据值
                数据行 = tuple(数据.get(字段) for 字段 in 字段列表)
                插入数据.append(数据行)

            # 批量插入
            await self.数据库连接池.执行数据库批量插入(插入查询, 插入数据)

            数据库日志器.debug(f"批量插入订单数据成功: 插入{len(插入数据)}条记录")
            return len(插入数据)

        except Exception as e:
            from 日志 import 数据库日志器

            数据库日志器.error(f"批量插入订单数据失败: {str(e)}")
            # 记录详细的错误信息用于调试
            if 订单数据列表:
                数据库日志器.error(f"第一条数据字段: {list(订单数据列表[0].keys())}")
            return 0


# 创建全局实例
店铺订单数据层实例 = 店铺订单数据层()
