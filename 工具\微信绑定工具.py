"""
微信绑定工具模块

提供微信账号绑定相关的工具函数，包括格式验证、重复检查等
"""

import re
from typing import Dict, Any, Optional, List
from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 日志 import 应用日志器, 错误日志器


class 微信绑定工具:
    """微信绑定相关的工具类"""
    
    @staticmethod
    def 验证微信号格式(微信号: str) -> Dict[str, Any]:
        """
        验证微信号格式
        
        Args:
            微信号: 需要验证的微信号
            
        Returns:
            Dict: 包含验证结果的字典
        """
        if not 微信号:
            return {"有效": False, "错误": "微信号不能为空"}
        
        # 去除首尾空格
        微信号 = 微信号.strip()
        
        # 长度检查
        if len(微信号) < 3:
            return {"有效": False, "错误": "微信号长度不能少于3个字符"}
        
        if len(微信号) > 30:
            return {"有效": False, "错误": "微信号长度不能超过30个字符"}
        
        # 格式检查：只允许字母、数字、下划线、连字符
        if not re.match(r'^[a-zA-Z0-9_-]+$', 微信号):
            return {"有效": False, "错误": "微信号只能包含字母、数字、下划线和连字符"}
        
        # 必须以字母或数字开头
        if not re.match(r'^[a-zA-Z0-9]', 微信号):
            return {"有效": False, "错误": "微信号必须以字母或数字开头"}
        
        return {"有效": True, "微信号": 微信号}
    
    @staticmethod
    async def 检查微信号重复绑定(微信号: str, 排除用户ID: Optional[int] = None) -> Dict[str, Any]:
        """
        检查微信号是否已被其他用户绑定
        
        Args:
            微信号: 需要检查的微信号
            排除用户ID: 排除检查的用户ID（当前用户）
            
        Returns:
            Dict: 包含检查结果的字典
        """
        try:
            检查SQL = """
            SELECT u.用户ID, w.微信号 
            FROM 用户微信关联表 u
            JOIN 微信信息表 w ON u.微信ID = w.id
            WHERE w.微信号 = %s AND u.状态 = 1
            """
            
            参数 = [微信号]
            
            # 如果指定了排除的用户ID，则添加条件
            if 排除用户ID:
                检查SQL += " AND u.用户ID != %s"
                参数.append(排除用户ID)
                
            结果 = await 异步连接池实例.执行查询(检查SQL, 参数)
            
            if 结果:
                return {
                    "已绑定": True,
                    "绑定用户ID": 结果[0]['用户ID'],
                    "错误": f"微信号 {微信号} 已被其他用户绑定"
                }
            
            return {"已绑定": False}
            
        except Exception as e:
            错误日志器.error(f"检查微信号重复绑定失败: {str(e)}")
            return {"已绑定": False, "检查失败": True, "错误": "检查服务异常"}
    
    @staticmethod
    async def 检查用户绑定状态(用户ID: int, 微信号: str) -> Dict[str, Any]:
        """
        检查用户与指定微信号的绑定状态
        
        Args:
            用户ID: 用户ID
            微信号: 微信号
            
        Returns:
            Dict: 包含绑定状态的字典
        """
        try:
            检查SQL = """
            SELECT u.id as 关联ID, u.状态, w.id as 微信ID
            FROM 用户微信关联表 u
            JOIN 微信信息表 w ON u.微信ID = w.id
            WHERE u.用户ID = %s AND w.微信号 = %s
            """
            
            结果 = await 异步连接池实例.执行查询(检查SQL, [用户ID, 微信号])
            
            if 结果:
                绑定记录 = 结果[0]
                return {
                    "存在绑定": True,
                    "关联ID": 绑定记录['关联ID'],
                    "微信ID": 绑定记录['微信ID'],
                    "当前状态": 绑定记录['状态'],
                    "是否激活": 绑定记录['状态'] == 1
                }
            
            return {"存在绑定": False}
            
        except Exception as e:
            错误日志器.error(f"检查用户绑定状态失败: {str(e)}")
            return {"存在绑定": False, "检查失败": True, "错误": "检查服务异常"}
    
    @staticmethod
    async def 获取用户绑定数量(用户ID: int) -> int:
        """
        获取用户当前绑定的微信账号数量
        
        Args:
            用户ID: 用户ID
            
        Returns:
            int: 绑定的微信账号数量
        """
        try:
            统计SQL = """
            SELECT COUNT(*) as 数量 
            FROM 用户微信关联表 
            WHERE 用户ID = %s AND 状态 = 1
            """
            
            结果 = await 异步连接池实例.执行查询(统计SQL, [用户ID])
            return 结果[0]['数量'] if 结果 else 0
            
        except Exception as e:
            错误日志器.error(f"获取用户绑定数量失败: {str(e)}")
            return 0
    
    @staticmethod
    def 验证备注内容(备注: str) -> Dict[str, Any]:
        """
        验证备注内容
        
        Args:
            备注: 备注内容
            
        Returns:
            Dict: 包含验证结果的字典
        """
        if not 备注:
            return {"有效": True, "备注": ""}
        
        # 去除首尾空格
        备注 = 备注.strip()
        
        # 长度检查
        if len(备注) > 100:
            return {"有效": False, "错误": "备注长度不能超过100个字符"}
        
        # 检查是否包含敏感内容（可以根据需要扩展）
        敏感词汇 = ["测试", "假的", "虚假"]  # 示例敏感词汇
        for 词汇 in 敏感词汇:
            if 词汇 in 备注:
                return {"有效": False, "错误": f"备注内容包含敏感词汇: {词汇}"}
        
        return {"有效": True, "备注": 备注}
    
    @staticmethod
    async def 获取用户绑定时间统计(用户ID: int) -> Dict[str, Any]:
        """
        获取用户绑定时间相关统计信息
        
        Args:
            用户ID: 用户ID
            
        Returns:
            Dict: 包含时间统计信息的字典
        """
        try:
            统计SQL = """
            SELECT 
                MIN(绑定时间) as 首次绑定时间,
                MAX(绑定时间) as 最近绑定时间,
                MAX(更新时间) as 最近更新时间,
                COUNT(CASE WHEN 绑定时间 >= CURRENT_TIMESTAMP - INTERVAL '7 days' THEN 1 END) as 近7天绑定数,
                COUNT(CASE WHEN 绑定时间 >= CURRENT_TIMESTAMP - INTERVAL '30 days' THEN 1 END) as 近30天绑定数,
                COUNT(CASE WHEN 更新时间 >= CURRENT_TIMESTAMP - INTERVAL '7 days' THEN 1 END) as 近7天更新数,
                AVG(EXTRACT(DAY FROM CURRENT_TIMESTAMP - 绑定时间)) as 平均绑定天数
            FROM 用户微信关联表
            WHERE 用户ID = $1 AND 状态 = 1
            """
            
            结果 = await 异步连接池实例.执行查询(统计SQL, [用户ID])
            
            if 结果 and 结果[0]:
                统计数据 = 结果[0]
                
                # 处理datetime对象
                for 字段 in ['首次绑定时间', '最近绑定时间', '最近更新时间']:
                    if 统计数据.get(字段):
                        统计数据[字段] = 统计数据[字段].strftime('%Y-%m-%d %H:%M:%S')
                
                # 处理decimal类型的平均天数
                if 统计数据.get('平均绑定天数'):
                    统计数据['平均绑定天数'] = round(float(统计数据['平均绑定天数']), 1)
                
                return {
                    "首次绑定时间": 统计数据.get('首次绑定时间'),
                    "最近绑定时间": 统计数据.get('最近绑定时间'),
                    "最近更新时间": 统计数据.get('最近更新时间'),
                    "近7天绑定数": 统计数据.get('近7天绑定数', 0),
                    "近30天绑定数": 统计数据.get('近30天绑定数', 0),
                    "近7天更新数": 统计数据.get('近7天更新数', 0),
                    "平均绑定天数": 统计数据.get('平均绑定天数', 0),
                    "绑定活跃度": "高" if 统计数据.get('近7天更新数', 0) > 0 else "低"
                }
            
            return {
                "首次绑定时间": None,
                "最近绑定时间": None,
                "最近更新时间": None,
                "近7天绑定数": 0,
                "近30天绑定数": 0,
                "近7天更新数": 0,
                "平均绑定天数": 0,
                "绑定活跃度": "无"
            }
            
        except Exception as e:
            错误日志器.error(f"获取用户绑定时间统计失败: {str(e)}")
            return {
                "首次绑定时间": None,
                "最近绑定时间": None,
                "最近更新时间": None,
                "近7天绑定数": 0,
                "近30天绑定数": 0,
                "近7天更新数": 0,
                "平均绑定天数": 0,
                "绑定活跃度": "未知"
            }
    
    @staticmethod
    async def 获取用户绑定详细列表(用户ID: int, 包含时间信息: bool = True) -> List[Dict[str, Any]]:
        """
        获取用户绑定的详细列表，包含完整的时间信息
        
        Args:
            用户ID: 用户ID
            包含时间信息: 是否包含详细的时间信息
            
        Returns:
            List[Dict]: 包含绑定详细信息的列表
        """
        try:
            if 包含时间信息:
                查询SQL = """
                SELECT 
                    uwx.id as 关联ID,
                    uwx.微信ID,
                    w.微信号,
                    uwx.绑定时间,
                    uwx.更新时间,
                    uwx.备注,
                    uwx.状态,
                    EXTRACT(DAY FROM CURRENT_TIMESTAMP - uwx.绑定时间) as 绑定天数,
                    CASE
                        WHEN uwx.更新时间 >= CURRENT_TIMESTAMP - INTERVAL '7 days' THEN '近期活跃'
                        WHEN uwx.更新时间 >= CURRENT_TIMESTAMP - INTERVAL '30 days' THEN '较少活跃'
                        ELSE '很少活跃'
                    END as 活跃状态
                FROM 用户微信关联表 uwx
                JOIN 微信信息表 w ON uwx.微信ID = w.id
                WHERE uwx.用户ID = $1 AND uwx.状态 = 1
                ORDER BY uwx.绑定时间 DESC
                """
            else:
                查询SQL = """
                SELECT 
                    uwx.id as 关联ID,
                    uwx.微信ID,
                    w.微信号,
                    uwx.备注,
                    uwx.状态
                FROM 用户微信关联表 uwx
                JOIN 微信信息表 w ON uwx.微信ID = w.id
                WHERE uwx.用户ID = $1 AND uwx.状态 = 1
                ORDER BY uwx.绑定时间 DESC
                """
            
            结果 = await 异步连接池实例.执行查询(查询SQL, [用户ID])
            
            # 处理结果
            绑定列表 = []
            for 记录 in 结果:
                绑定信息 = {
                    "关联ID": 记录['关联ID'],
                    "微信ID": 记录['微信ID'],
                    "微信号": 记录['微信号'],
                    "备注": 记录.get('备注', ''),
                    "状态": 记录['状态']
                }
                
                if 包含时间信息:
                    绑定信息.update({
                        "绑定时间": 记录['绑定时间'].strftime('%Y-%m-%d %H:%M:%S') if 记录.get('绑定时间') else None,
                        "更新时间": 记录['更新时间'].strftime('%Y-%m-%d %H:%M:%S') if 记录.get('更新时间') else None,
                        "绑定天数": 记录.get('绑定天数', 0),
                        "活跃状态": 记录.get('活跃状态', '未知')
                    })
                
                绑定列表.append(绑定信息)
                
            return 绑定列表
            
        except Exception as e:
            错误日志器.error(f"获取用户绑定详细列表失败: {str(e)}")
            return []


# 快速访问的工具函数
async def 快速检查微信号格式(微信号: str) -> bool:
    """快速检查微信号格式是否有效"""
    结果 = 微信绑定工具.验证微信号格式(微信号)
    return 结果.get("有效", False)


async def 快速检查重复绑定(微信号: str, 排除用户ID: Optional[int] = None) -> bool:
    """快速检查微信号是否重复绑定"""
    结果 = await 微信绑定工具.检查微信号重复绑定(微信号, 排除用户ID)
    return 结果.get("已绑定", False)


async def 快速获取绑定数量(用户ID: int) -> int:
    """快速获取用户绑定数量"""
    return await 微信绑定工具.获取用户绑定数量(用户ID) 