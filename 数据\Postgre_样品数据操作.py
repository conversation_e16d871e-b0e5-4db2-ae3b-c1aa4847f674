"""
PostgreSQL样品数据操作模块
基于asyncpg实现的样品管理相关数据库操作

功能：
1. 样品基础CRUD操作
2. 样品状态管理
3. 样品分类和搜索
4. 样品统计分析
"""

import json
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union

from 数据.Postgre_异步连接池 import Postgre_异步连接池实例
from 日志 import 数据库日志器, 错误日志器


# ==================== 样品基础操作 ====================

async def Postgre_创建样品(
    样品名称: str,
    样品类型: str,
    创建人ID: int,
    样品描述: Optional[str] = None,
    样品规格: Optional[str] = None,
    样品数量: int = 1,
    样品单位: str = "个",
    存储位置: Optional[str] = None,
    团队ID: Optional[int] = None
) -> Optional[int]:
    """
    创建新样品
    
    Args:
        样品名称: 样品名称
        样品类型: 样品类型
        创建人ID: 创建人用户ID
        样品描述: 样品描述
        样品规格: 样品规格
        样品数量: 样品数量
        样品单位: 样品单位
        存储位置: 存储位置
        团队ID: 所属团队ID
        
    Returns:
        新创建样品的ID，失败返回None
    """
    try:
        插入SQL = """
        INSERT INTO 样品表 (
            样品名称, 样品类型, 样品描述, 样品规格, 样品数量, 样品单位,
            存储位置, 样品状态, 创建人ID, 团队ID, created_at
        ) VALUES (
            $1, $2, $3, $4, $5, $6, $7, '正常', $8, $9, CURRENT_TIMESTAMP
        ) RETURNING id
        """
        
        参数 = (
            样品名称, 样品类型, 样品描述, 样品规格, 样品数量, 
            样品单位, 存储位置, 创建人ID, 团队ID
        )
        
        async with Postgre_异步连接池实例.获取连接() as 连接:
            结果 = await 连接.fetchrow(插入SQL, *参数)
            样品ID = 结果['id'] if 结果 else None
            
            if 样品ID:
                数据库日志器.info(f"样品创建成功: ID={样品ID}, 名称={样品名称}")
                return 样品ID
            else:
                错误日志器.error("样品创建失败，未返回样品ID")
                return None
                
    except Exception as e:
        错误日志器.error(f"创建样品失败: {str(e)}")
        return None


async def Postgre_获取样品信息(样品ID: int) -> Optional[Dict[str, Any]]:
    """
    获取样品详细信息
    
    Args:
        样品ID: 样品ID
        
    Returns:
        样品信息字典，不存在返回None
    """
    try:
        查询SQL = """
        SELECT s.id, s.样品名称, s.样品类型, s.样品描述, s.样品规格,
               s.样品数量, s.样品单位, s.存储位置, s.样品状态,
               s.创建人ID, s.团队ID, s.created_at, s.updated_at,
               u.昵称 as 创建人昵称, t.团队名称
        FROM 样品表 s
        LEFT JOIN 用户表 u ON s.创建人ID = u.id
        LEFT JOIN 团队表 t ON s.团队ID = t.id
        WHERE s.id = $1
        """
        
        async with Postgre_异步连接池实例.获取连接() as 连接:
            结果 = await 连接.fetchrow(查询SQL, 样品ID)
            
            if 结果:
                样品信息 = dict(结果)
                # 处理datetime序列化
                if 样品信息.get("created_at"):
                    样品信息["created_at"] = 样品信息["created_at"].isoformat()
                if 样品信息.get("updated_at"):
                    样品信息["updated_at"] = 样品信息["updated_at"].isoformat()
                
                数据库日志器.debug(f"获取样品信息成功: ID={样品ID}")
                return 样品信息
            else:
                数据库日志器.warning(f"样品不存在: ID={样品ID}")
                return None
                
    except Exception as e:
        错误日志器.error(f"获取样品信息失败: {str(e)}")
        return None


async def Postgre_更新样品信息(
    样品ID: int,
    更新数据: Dict[str, Any],
    操作人ID: int
) -> bool:
    """
    更新样品信息
    
    Args:
        样品ID: 样品ID
        更新数据: 要更新的字段和值
        操作人ID: 操作人用户ID
        
    Returns:
        是否成功
    """
    try:
        # 构建动态更新SQL
        更新字段 = []
        参数值 = []
        参数索引 = 1
        
        # 允许更新的字段
        允许更新字段 = {
            "样品名称": "样品名称",
            "样品类型": "样品类型",
            "样品描述": "样品描述",
            "样品规格": "样品规格",
            "样品数量": "样品数量",
            "样品单位": "样品单位",
            "存储位置": "存储位置",
            "样品状态": "样品状态"
        }
        
        for 字段名, SQL字段 in 允许更新字段.items():
            if 字段名 in 更新数据:
                更新字段.append(f"{SQL字段} = ${参数索引}")
                参数值.append(更新数据[字段名])
                参数索引 += 1
        
        # 添加更新时间
        更新字段.append(f"updated_at = ${参数索引}")
        参数值.append(datetime.now())
        参数索引 += 1
        
        # 添加样品ID作为WHERE条件
        参数值.append(样品ID)
        
        if not 更新字段:
            数据库日志器.warning("没有需要更新的字段")
            return False
        
        更新SQL = f"""
        UPDATE 样品表 
        SET {', '.join(更新字段)}
        WHERE id = ${参数索引}
        """
        
        async with Postgre_异步连接池实例.获取连接() as 连接:
            结果 = await 连接.execute(更新SQL, *参数值)
            影响行数 = int(结果.split()[-1]) if 结果.startswith('UPDATE') else 0
            
            if 影响行数 > 0:
                数据库日志器.info(f"样品信息更新成功: ID={样品ID}, 操作人={操作人ID}")
                return True
            else:
                数据库日志器.warning(f"样品信息更新失败，可能不存在: ID={样品ID}")
                return False
                
    except Exception as e:
        错误日志器.error(f"更新样品信息失败: {str(e)}")
        return False


async def Postgre_删除样品(样品ID: int, 操作人ID: int) -> bool:
    """
    删除样品（软删除，更新状态为已删除）
    
    Args:
        样品ID: 样品ID
        操作人ID: 操作人用户ID
        
    Returns:
        是否成功
    """
    try:
        更新SQL = """
        UPDATE 样品表 
        SET 样品状态 = '已删除', updated_at = CURRENT_TIMESTAMP 
        WHERE id = $1
        """
        
        async with Postgre_异步连接池实例.获取连接() as 连接:
            结果 = await 连接.execute(更新SQL, 样品ID)
            影响行数 = int(结果.split()[-1]) if 结果.startswith('UPDATE') else 0
            
            if 影响行数 > 0:
                数据库日志器.info(f"样品删除成功: ID={样品ID}, 操作人={操作人ID}")
                return True
            else:
                数据库日志器.warning(f"样品删除失败，可能不存在: ID={样品ID}")
                return False
                
    except Exception as e:
        错误日志器.error(f"删除样品失败: {str(e)}")
        return False


# ==================== 样品查询和列表 ====================

async def Postgre_获取样品列表(
    页码: int = 1,
    每页数量: int = 20,
    搜索关键词: Optional[str] = None,
    样品类型筛选: Optional[str] = None,
    状态筛选: Optional[str] = None,
    团队ID筛选: Optional[int] = None,
    创建人ID筛选: Optional[int] = None
) -> tuple[List[Dict[str, Any]], int]:
    """
    获取样品列表（支持分页、搜索、筛选）
    
    Args:
        页码: 页码（从1开始）
        每页数量: 每页显示数量
        搜索关键词: 搜索关键词（支持样品名称、描述）
        样品类型筛选: 样品类型筛选
        状态筛选: 状态筛选
        团队ID筛选: 团队ID筛选
        创建人ID筛选: 创建人ID筛选
        
    Returns:
        (样品列表, 总数量)
    """
    try:
        # 构建WHERE条件
        where_条件 = []
        参数值 = []
        参数索引 = 1
        
        # 搜索条件
        if 搜索关键词:
            where_条件.append(f"(s.样品名称 ILIKE ${参数索引} OR s.样品描述 ILIKE ${参数索引 + 1})")
            参数值.extend([f"%{搜索关键词}%", f"%{搜索关键词}%"])
            参数索引 += 2
        
        # 样品类型筛选
        if 样品类型筛选:
            where_条件.append(f"s.样品类型 = ${参数索引}")
            参数值.append(样品类型筛选)
            参数索引 += 1
            
        # 状态筛选
        if 状态筛选:
            where_条件.append(f"s.样品状态 = ${参数索引}")
            参数值.append(状态筛选)
            参数索引 += 1
            
        # 团队筛选
        if 团队ID筛选:
            where_条件.append(f"s.团队ID = ${参数索引}")
            参数值.append(团队ID筛选)
            参数索引 += 1
            
        # 创建人筛选
        if 创建人ID筛选:
            where_条件.append(f"s.创建人ID = ${参数索引}")
            参数值.append(创建人ID筛选)
            参数索引 += 1
        
        where_clause = "WHERE " + " AND ".join(where_条件) if where_条件 else ""
        
        # 查询总数
        计数SQL = f"""
        SELECT COUNT(*) as total_count 
        FROM 样品表 s 
        {where_clause}
        """
        
        # 查询数据
        偏移量 = (页码 - 1) * 每页数量
        查询SQL = f"""
        SELECT s.id, s.样品名称, s.样品类型, s.样品数量, s.样品单位,
               s.存储位置, s.样品状态, s.created_at,
               u.昵称 as 创建人昵称, t.团队名称
        FROM 样品表 s
        LEFT JOIN 用户表 u ON s.创建人ID = u.id
        LEFT JOIN 团队表 t ON s.团队ID = t.id
        {where_clause}
        ORDER BY s.created_at DESC
        LIMIT ${参数索引} OFFSET ${参数索引 + 1}
        """
        
        async with Postgre_异步连接池实例.获取连接() as 连接:
            # 获取总数
            总数结果 = await 连接.fetchrow(计数SQL, *参数值)
            总数量 = 总数结果['total_count'] if 总数结果 else 0
            
            # 获取数据
            查询参数 = 参数值 + [每页数量, 偏移量]
            样品列表 = await 连接.fetch(查询SQL, *查询参数)
            
            # 转换结果
            结果列表 = []
            for 样品 in 样品列表:
                样品信息 = dict(样品)
                # 处理datetime序列化
                if 样品信息.get("created_at"):
                    样品信息["created_at"] = 样品信息["created_at"].isoformat()
                结果列表.append(样品信息)
            
            数据库日志器.debug(f"样品列表查询完成: 关键词={搜索关键词}, 返回{len(结果列表)}/{总数量}条记录")
            return 结果列表, 总数量
            
    except Exception as e:
        错误日志器.error(f"获取样品列表失败: {str(e)}")
        return [], 0


async def Postgre_获取用户样品列表(
    用户ID: int,
    页码: int = 1,
    每页数量: int = 20,
    状态筛选: Optional[str] = None
) -> tuple[List[Dict[str, Any]], int]:
    """
    获取用户创建的样品列表
    
    Args:
        用户ID: 用户ID
        页码: 页码
        每页数量: 每页数量
        状态筛选: 状态筛选
        
    Returns:
        (样品列表, 总数量)
    """
    try:
        # 构建WHERE条件
        where_条件 = ["s.创建人ID = $1"]
        参数值 = [用户ID]
        参数索引 = 2
        
        if 状态筛选:
            where_条件.append(f"s.样品状态 = ${参数索引}")
            参数值.append(状态筛选)
            参数索引 += 1
        
        where_clause = " AND ".join(where_条件)
        
        # 查询总数
        计数SQL = f"""
        SELECT COUNT(*) as total 
        FROM 样品表 s 
        WHERE {where_clause}
        """
        
        # 查询数据
        偏移量 = (页码 - 1) * 每页数量
        查询SQL = f"""
        SELECT s.id, s.样品名称, s.样品类型, s.样品数量, s.样品单位,
               s.存储位置, s.样品状态, s.created_at,
               t.团队名称
        FROM 样品表 s
        LEFT JOIN 团队表 t ON s.团队ID = t.id
        WHERE {where_clause}
        ORDER BY s.created_at DESC
        LIMIT ${参数索引} OFFSET ${参数索引 + 1}
        """
        
        async with Postgre_异步连接池实例.获取连接() as 连接:
            # 获取总数
            总数结果 = await 连接.fetchrow(计数SQL, *参数值)
            总数量 = 总数结果['total'] if 总数结果 else 0
            
            # 获取数据
            查询参数 = 参数值 + [每页数量, 偏移量]
            样品列表 = await 连接.fetch(查询SQL, *查询参数)
            
            # 转换结果
            结果列表 = []
            for 样品 in 样品列表:
                样品信息 = dict(样品)
                # 处理datetime序列化
                if 样品信息.get("created_at"):
                    样品信息["created_at"] = 样品信息["created_at"].isoformat()
                结果列表.append(样品信息)
            
            数据库日志器.debug(f"用户样品列表查询完成: 用户ID={用户ID}, 返回{len(结果列表)}/{总数量}条记录")
            return 结果列表, 总数量
            
    except Exception as e:
        错误日志器.error(f"获取用户样品列表失败: {str(e)}")
        return [], 0


# ==================== 样品统计操作 ====================

async def Postgre_获取样品统计信息(团队ID: Optional[int] = None) -> Dict[str, Any]:
    """
    获取样品统计信息
    
    Args:
        团队ID: 团队ID（可选，为None时统计全部）
        
    Returns:
        统计信息字典
    """
    try:
        where_clause = "WHERE 团队ID = $1" if 团队ID else ""
        参数 = [团队ID] if 团队ID else []
        
        统计SQL = f"""
        SELECT 
            COUNT(*) as 样品总数,
            COUNT(CASE WHEN 样品状态 = '正常' THEN 1 END) as 正常样品数,
            COUNT(CASE WHEN 样品状态 = '已删除' THEN 1 END) as 已删除样品数,
            COUNT(DISTINCT 样品类型) as 样品类型数,
            COUNT(DISTINCT 创建人ID) as 创建人数,
            SUM(样品数量) as 样品总数量
        FROM 样品表
        {where_clause}
        """
        
        async with Postgre_异步连接池实例.获取连接() as 连接:
            结果 = await 连接.fetchrow(统计SQL, *参数)
            
            if 结果:
                统计信息 = dict(结果)
                数据库日志器.debug(f"样品统计信息查询完成: 团队ID={团队ID}")
                return 统计信息
            else:
                return {
                    "样品总数": 0,
                    "正常样品数": 0,
                    "已删除样品数": 0,
                    "样品类型数": 0,
                    "创建人数": 0,
                    "样品总数量": 0
                }
                
    except Exception as e:
        错误日志器.error(f"获取样品统计信息失败: {str(e)}")
        return {
            "样品总数": 0,
            "正常样品数": 0,
            "已删除样品数": 0,
            "样品类型数": 0,
            "创建人数": 0,
            "样品总数量": 0
        }


async def Postgre_获取样品类型统计(团队ID: Optional[int] = None) -> List[Dict[str, Any]]:
    """
    获取样品类型统计
    
    Args:
        团队ID: 团队ID（可选）
        
    Returns:
        样品类型统计列表
    """
    try:
        where_clause = "WHERE 团队ID = $1 AND 样品状态 = '正常'" if 团队ID else "WHERE 样品状态 = '正常'"
        参数 = [团队ID] if 团队ID else []
        
        统计SQL = f"""
        SELECT 样品类型, COUNT(*) as 数量, SUM(样品数量) as 总数量
        FROM 样品表
        {where_clause}
        GROUP BY 样品类型
        ORDER BY 数量 DESC
        """
        
        async with Postgre_异步连接池实例.获取连接() as 连接:
            结果列表 = await 连接.fetch(统计SQL, *参数)
            
            # 转换结果
            统计列表 = [dict(行) for 行 in 结果列表]
            
            数据库日志器.debug(f"样品类型统计查询完成: 团队ID={团队ID}, 返回{len(统计列表)}种类型")
            return 统计列表
            
    except Exception as e:
        错误日志器.error(f"获取样品类型统计失败: {str(e)}")
        return []


async def Postgre_搜索样品(
    搜索关键词: str,
    页码: int = 1,
    每页数量: int = 20,
    样品类型筛选: Optional[str] = None,
    状态筛选: Optional[str] = None
) -> tuple[List[Dict[str, Any]], int]:
    """
    搜索样品

    Args:
        搜索关键词: 搜索关键词
        页码: 页码
        每页数量: 每页数量
        样品类型筛选: 样品类型筛选
        状态筛选: 状态筛选

    Returns:
        (样品列表, 总数量)
    """
    try:
        # 构建WHERE条件
        where_条件 = []
        参数值 = []
        参数索引 = 1

        # 搜索关键词
        if 搜索关键词:
            where_条件.append(f"(s.样品名称 LIKE ${参数索引} OR s.样品描述 LIKE ${参数索引})")
            参数值.append(f"%{搜索关键词}%")
            参数索引 += 1

        # 样品类型筛选
        if 样品类型筛选:
            where_条件.append(f"s.样品类型 = ${参数索引}")
            参数值.append(样品类型筛选)
            参数索引 += 1

        # 状态筛选
        if 状态筛选:
            where_条件.append(f"s.样品状态 = ${参数索引}")
            参数值.append(状态筛选)
            参数索引 += 1

        where_clause = "WHERE " + " AND ".join(where_条件) if where_条件 else ""

        # 查询总数
        计数SQL = f"""
        SELECT COUNT(*) as total_count
        FROM 样品表 s
        {where_clause}
        """

        # 查询数据
        偏移量 = (页码 - 1) * 每页数量
        查询SQL = f"""
        SELECT s.id, s.样品名称, s.样品类型, s.样品数量, s.样品单位,
               s.存储位置, s.样品状态, s.created_at,
               u.昵称 as 创建人昵称, t.团队名称
        FROM 样品表 s
        LEFT JOIN 用户表 u ON s.创建人ID = u.id
        LEFT JOIN 团队表 t ON s.团队ID = t.id
        {where_clause}
        ORDER BY s.created_at DESC
        LIMIT ${参数索引} OFFSET ${参数索引 + 1}
        """

        async with Postgre_异步连接池实例.获取连接() as 连接:
            # 获取总数
            总数结果 = await 连接.fetchrow(计数SQL, *参数值)
            总数量 = 总数结果['total_count'] if 总数结果 else 0

            # 获取数据
            查询参数 = 参数值 + [每页数量, 偏移量]
            样品列表 = await 连接.fetch(查询SQL, *查询参数)

            # 转换结果
            结果列表 = []
            for 样品 in 样品列表:
                样品信息 = dict(样品)
                # 处理datetime序列化
                if 样品信息.get("created_at"):
                    样品信息["created_at"] = 样品信息["created_at"].isoformat()
                结果列表.append(样品信息)

            数据库日志器.debug(f"搜索样品完成: 关键词={搜索关键词}, 返回{len(结果列表)}/{总数量}条记录")
            return 结果列表, 总数量

    except Exception as e:
        错误日志器.error(f"搜索样品失败: {str(e)}")
        return [], 0


async def Postgre_获取样品详情(样品ID: int) -> Optional[Dict[str, Any]]:
    """
    获取样品详情（包含关联信息）

    Args:
        样品ID: 样品ID

    Returns:
        样品详情信息
    """
    try:
        查询SQL = """
        SELECT s.*,
               u.昵称 as 创建人昵称, u.手机号 as 创建人手机号,
               t.团队名称
        FROM 样品表 s
        LEFT JOIN 用户表 u ON s.创建人ID = u.id
        LEFT JOIN 团队表 t ON s.团队ID = t.id
        WHERE s.id = $1
        """

        async with Postgre_异步连接池实例.获取连接() as 连接:
            结果 = await 连接.fetchrow(查询SQL, 样品ID)

            if 结果:
                样品详情 = dict(结果)
                # 处理datetime序列化
                if 样品详情.get("created_at"):
                    样品详情["created_at"] = 样品详情["created_at"].isoformat()
                if 样品详情.get("updated_at"):
                    样品详情["updated_at"] = 样品详情["updated_at"].isoformat()

                数据库日志器.debug(f"获取样品详情成功: 样品ID={样品ID}")
                return 样品详情
            else:
                数据库日志器.debug(f"样品不存在: 样品ID={样品ID}")
                return None

    except Exception as e:
        错误日志器.error(f"获取样品详情失败: {str(e)}")
        return None


async def Postgre_更新样品状态(样品ID: int, 新状态: str, 操作人ID: int) -> bool:
    """
    更新样品状态

    Args:
        样品ID: 样品ID
        新状态: 新的状态
        操作人ID: 操作人ID

    Returns:
        更新是否成功
    """
    try:
        更新SQL = """
        UPDATE 样品表
        SET 样品状态 = $2, updated_at = CURRENT_TIMESTAMP
        WHERE id = $1
        """

        async with Postgre_异步连接池实例.获取连接() as 连接:
            结果 = await 连接.execute(更新SQL, 样品ID, 新状态)
            影响行数 = int(结果.split()[-1]) if 结果.startswith('UPDATE') else 0

            if 影响行数 > 0:
                数据库日志器.info(f"样品状态更新成功: 样品ID={样品ID}, 新状态={新状态}, 操作人={操作人ID}")
                return True
            else:
                数据库日志器.warning(f"样品状态更新失败，可能样品不存在: 样品ID={样品ID}")
                return False

    except Exception as e:
        错误日志器.error(f"更新样品状态异常: {str(e)}")
        return False
