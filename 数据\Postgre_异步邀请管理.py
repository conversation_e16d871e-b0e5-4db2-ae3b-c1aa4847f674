"""
异步邀请管理数据库操作函数 - PostgreSQL版本
提供团队邀请相关的数据库操作功能
主要功能：1. 通过手机号邀请成员（支持已注册和未注册用户）
2. 重复邀请智能处理（更新现有邀请而非报错）
3. 邀请状态跟踪（新建、已存在、已更新）
4. 邀请链接生成和管理
5. 邀请记录查询和管理

新增改进（2024-12）：
- 当检测到待处理邀请时，返回现有邀请信息而不是失败
- 支持邀请状态区分（new/existing/updated）
- 自动更新邀请过期时间，确保邀请有效
- 优化用户体验，减少重复操作困扰
"""

import os
import secrets
import string
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any

from 数据.Postgre_异步数据库函数 import Postgre_异步获取用户_电话, Postgre_异步创建半注册用户, Postgre_异步用户是否已注册_电话, Postgre_异步获取用户_id
# 导入数据库连接和日志
from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 日志 import 错误日志器, 数据库日志器

# 邀请状态常量（适配现有数据库枚举值）
INVITATION_STATUS = {
    "PENDING": "邀请待处理",
    "ACCEPTED": "正常", 
    "REJECTED": "已拒绝邀请",
    "EXPIRED": "已拒绝邀请",  # 过期的邀请标记为已拒绝
    "REVOKED": "已移除"
}

# 中文邀请类型枚举
INVITATION_TYPE_CN = {
    "direct": "直接邀请",
    "registration": "注册邀请"
}

# 中文邀请状态枚举（用于返回给前端的状态标识）
INVITATION_STATUS_CN = {
    "new": "新建邀请",
    "existing": "已存在邀请", 
    "updated": "已更新邀请"
}

def Postgre_处理邀请创建异常(e: Exception, context: str) -> Dict[str, Any]:
    """统一处理邀请创建过程中的异常"""
    错误消息 = str(e)
    if "generator" in 错误消息.lower() or "didn't stop after athrow" in 错误消息:
        错误日志器.error(f"{context}时检测到生成器错误: {错误消息}", exc_info=True)
        return {"success": False, "message": "数据库连接不稳定，请稍后重试"}
    if any(关键词 in 错误消息.lower() for 关键词 in ["connection", "timeout", "pool"]):
        错误日志器.error(f"{context}时检测到连接错误: {错误消息}", exc_info=True)
        return {"success": False, "message": "网络连接不稳定，请稍后重试"}
    错误日志器.error(f"{context}失败: {错误消息}", exc_info=True)
    return {"success": False, "message": f"{context}失败: {错误消息}"}

def Postgre_解析备注信息(备注: str) -> Dict[str, Any]:
    """从备注字符串中解析手机号、邀请令牌和权限列表"""
    手机号 = None
    邀请令牌 = None
    权限列表 = []
    if 备注:
        备注部分 = 备注.split('|')
        for 部分 in 备注部分:
            if 部分.startswith('手机号:'):
                手机号 = 部分.replace('手机号:', '')
            elif 部分.startswith('令牌:'):
                邀请令牌 = 部分.replace('令牌:', '')
            elif 部分.startswith('权限:'):
                权限文本 = 部分.replace('权限:', '')
                if 权限文本:
                    权限列表 = 权限文本.split(',')
    return {"手机号": 手机号, "邀请令牌": 邀请令牌, "权限列表": 权限列表}

def Postgre_生成邀请令牌(长度: int = 32) -> str:
    """生成唯一的邀请令牌"""
    字符集 = string.ascii_letters + string.digits
    return ''.join(secrets.choice(字符集) for _ in range(长度))

async def Postgre_异步通过手机号邀请成员(
    手机号: str,
    团队ID: int,
    邀请人ID: int,
    角色: str = "成员",
    权限列表: Optional[List[str]] = None
) -> Dict[str, Any]:
    """
    通过手机号邀请成员
    检查用户是否注册，已注册发送直接邀请，未注册生成注册邀请链接
    """
    try:
        数据库日志器.info(f"开始处理手机号邀请：{手机号} -> 团队 {团队ID}")
        
        # 安全检查：不允许通过邀请设置创建者角色
        if 角色 in ["创建者", "创始人", "founder", "creator"]:
            错误日志器.warning(f"尝试通过邀请设置创建者角色：用户 {邀请人ID} 邀请 {手机号} 为 {角色}")
            return {
                "success": False,
                "message": "邀请成员时不能设置为创建者角色，每个团队只能有一个创建者"
            }
        
        # 检查用户注册状态
        用户数据 = await Postgre_异步获取用户_电话(手机号)
        用户已完成注册 = await Postgre_异步用户是否已注册_电话(手机号)
        
        if 用户数据 and 用户已完成注册:
            # 用户已完成注册，发送直接邀请
            数据库日志器.info(f"用户 {手机号} 已完成注册，发送直接邀请")
            return await Postgre_异步创建直接邀请(
                被邀请用户ID=用户数据['id'],
                团队ID=团队ID,
                邀请人ID=邀请人ID,
                角色=角色,
                权限列表=权限列表
            )
        else:
            # 用户未注册或为半注册状态，需要创建/获取用户ID并生成注册邀请链接
            if 用户数据 and 用户数据.get('状态') == '未注册':
                # 已存在半注册用户记录
                数据库日志器.info(f"用户 {手机号} 为半注册状态，使用现有用户ID {用户数据['id']}")
                用户ID = 用户数据['id']
            else:
                # 完全未注册，创建半注册用户
                数据库日志器.info(f"用户 {手机号} 完全未注册，创建半注册用户记录")
                try:
                    用户ID = await Postgre_异步创建半注册用户(手机号)
                    if not 用户ID or 用户ID <= 0:
                        错误日志器.error(f"创建半注册用户失败：返回无效用户ID ({用户ID}) for 手机号 {手机号}")
                        return {
                            "success": False,
                            "message": "创建用户记录失败，请稍后重试或联系管理员"
                        }
                    数据库日志器.info(f"成功创建半注册用户，用户ID: {用户ID}")
                except Exception as 创建用户异常:
                    错误日志器.error(f"创建半注册用户异常：{创建用户异常}, 手机号: {手机号}")
                    return {
                        "success": False,
                        "message": f"创建用户记录失败：{str(创建用户异常)}"
                    }
            
            # 验证用户ID有效性
            if not 用户ID or 用户ID <= 0:
                错误日志器.error(f"获取到无效用户ID: {用户ID}, 手机号: {手机号}")
                return {
                    "success": False,
                    "message": "用户ID无效，无法创建邀请"
                }
            
            # 创建直接邀请（使用半注册用户ID）
            数据库日志器.info(f"为半注册用户 {手机号}（用户ID: {用户ID}）创建邀请")
            邀请结果 = await Postgre_异步创建直接邀请(
                被邀请用户ID=用户ID,
                团队ID=团队ID,
                邀请人ID=邀请人ID,
                角色=角色,
                权限列表=权限列表
            )
            
            # 增强邀请结果验证
            if not 邀请结果.get("success"):
                错误日志器.error(f"创建邀请失败，手机号: {手机号}, 用户ID: {用户ID}, 错误: {邀请结果.get('message')}")
            
            return 邀请结果
            
    except Exception as e:
        return Postgre_处理邀请创建异常(e, "通过手机号邀请成员")

async def Postgre_异步创建直接邀请(
    被邀请用户ID: int,
    团队ID: int,
    邀请人ID: int,
    角色: str = "成员",
    权限列表: Optional[List[str]] = None
) -> Dict[str, Any]:
    """创建直接邀请记录（使用用户团队关联表）"""
    try:
        # 【修复关键问题】验证用户ID的有效性，防止NULL值插入数据库
        if not 被邀请用户ID or 被邀请用户ID <= 0:
            错误日志器.error(f"创建直接邀请失败：被邀请用户ID无效 ({被邀请用户ID})")
            return {
                "success": False,
                "message": "邀请失败：用户ID无效，请检查用户是否存在"
            }
        
        if not 团队ID or 团队ID <= 0:
            错误日志器.error(f"创建直接邀请失败：团队ID无效 ({团队ID})")
            return {
                "success": False,
                "message": "邀请失败：团队ID无效"
            }
        
        if not 邀请人ID or 邀请人ID <= 0:
            错误日志器.error(f"创建直接邀请失败：邀请人ID无效 ({邀请人ID})")
            return {
                "success": False,
                "message": "邀请失败：邀请人ID无效"
            }
        
        数据库日志器.info(f"开始创建直接邀请：用户ID={被邀请用户ID}, 团队ID={团队ID}, 邀请人ID={邀请人ID}, 角色={角色}")
        
        # 检查是否已存在关联记录
        检查SQL = """
        SELECT id, 状态 FROM 用户团队关联表
        WHERE 用户ID = $1 AND 团队ID = $2
        """
        
        数据库日志器.info(f"执行查询SQL: {检查SQL}, 参数: ({被邀请用户ID}, {团队ID})")
        现有记录结果 = await 异步连接池实例.执行查询(检查SQL, (被邀请用户ID, 团队ID))
        现有记录 = 现有记录结果[0] if 现有记录结果 else None
        
        # 添加安全检查和详细日志
        数据库日志器.info(f"查询结果: {现有记录}, 类型: {type(现有记录)}")
        
        if 现有记录:
            记录ID = 现有记录['id']
            记录状态 = 现有记录['状态']
            
            数据库日志器.info(f"记录ID: {记录ID}, 记录状态 {记录状态}")
            
            if 记录状态 == INVITATION_STATUS["ACCEPTED"]:
                return {
                    "success": True,
                    "message": "该用户已经是团队成员",
                    "data": {
                        "邀请状态": "已是成员",
                        "友好提示": True
                    }
                }
            
            # 对于PENDING状态，更新邀请信息并返回现有邀请详情
            if 记录状态 == INVITATION_STATUS["PENDING"]:
                数据库日志器.info(f"检测到待处理邀请，将更新邀请信息并返回邀请链接")
        
        # 生成邀请令牌（用于前端显示和邀请链接）
        邀请令牌 = Postgre_生成邀请令牌()
        
        if 现有记录:
            # 更新现有记录
            更新SQL = """
            UPDATE 用户团队关联表 SET
                状态 = $1, 邀请人ID = $2,
                邀请过期时间 = $3, 职位 = $4, 备注 = $5, 更新时间 = $6
            WHERE id = $7
            """
            
            过期时间 = datetime.now() + timedelta(days=7)
            
            # 为了获取手机号，需要查询一下用户信息
            被邀请用户信息 = await Postgre_异步获取用户_id(被邀请用户ID)
            被邀请人手机号 = 被邀请用户信息.get('phone', '') if 被邀请用户信息 else ''
            
            # 生成备注信息，包含邀请令牌和手机号
            权限JSON = ','.join(权限列表) if 权限列表 else ''
            直接邀请备份 = f"直接邀请|手机号:{被邀请人手机号}|令牌:{邀请令牌}|权限:{权限JSON}"
            
            await 异步连接池实例.执行更新(更新SQL, (
                INVITATION_STATUS["PENDING"], 邀请人ID,
                过期时间, 角色, 直接邀请备份, datetime.now(), 记录ID
            ))
            
            关联ID = 记录ID
            
            # 如果是更新已有的PENDING邀请，标记为现有邀请
            邀请状态_标记 = "existing" if 记录状态 == INVITATION_STATUS["PENDING"] else "updated"
        else:
            # 插入新记录
            插入SQL = """
            INSERT INTO 用户团队关联表(
                用户ID, 团队ID, 邀请人ID, 职位, 
                状态, 邀请过期时间, 
                备注, 加入时间, 创建时间
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
            RETURNING id
            """
            
            过期时间 = datetime.now() + timedelta(days=7)

            # 为了获取手机号，需要查询一下用户信息
            被邀请用户信息 = await Postgre_异步获取用户_id(被邀请用户ID)
            被邀请人手机号 = 被邀请用户信息.get('phone', '') if 被邀请用户信息 else ''

            # 生成备注信息，包含邀请令牌和手机号
            权限JSON = ','.join(权限列表) if 权限列表 else ''
            直接邀请备份 = f"直接邀请|手机号:{被邀请人手机号}|令牌:{邀请令牌}|权限:{权限JSON}"

            关联ID = await 异步连接池实例.执行插入(插入SQL, (
                被邀请用户ID, 团队ID, 邀请人ID, 角色,
                INVITATION_STATUS["PENDING"], 过期时间,
                直接邀请备份, datetime.now(), datetime.now()
            ))

            邀请状态_标记 = "new"

        # 生成邀请链接
        前端域名 = os.getenv('FRONTEND_URL', 'https://crm.limob.cn')
        邀请链接 = f"{前端域名}/invitation?token={邀请令牌}&teamId={团队ID}"

        数据库日志器.info(f"直接邀请创建成功，关联ID: {关联ID}, 邀请状态: {邀请状态_标记}")

        return {
            "success": True,
            "message": f"邀请已发送",
            "data": {
                "关联ID": 关联ID,
                "邀请令牌": 邀请令牌,
                "邀请链接": 邀请链接,
                "过期时间": 过期时间.isoformat(),
                "邀请状态": INVITATION_STATUS_CN[邀请状态_标记],
                "邀请类型": INVITATION_TYPE_CN["direct"],
                "被邀请用户ID": 被邀请用户ID,
                "团队ID": 团队ID,
                "角色": 角色,
                "权限列表": 权限列表 or []
            }
        }

    except Exception as e:
        return Postgre_处理邀请创建异常(e, "创建直接邀请")

async def Postgre_异步获取团队邀请列表(
    团队ID: int,
    页码: int = 1,
    每页数量: int = 20,
    状态筛选: Optional[str] = None
) -> Dict[str, Any]:
    """获取团队邀请列表"""
    try:
        数据库日志器.info(f"获取团队邀请列表 团队ID={团队ID}, 页码={页码}, 状态筛选={状态筛选}")

        # 构建查询条件
        条件列表 = ["uta.团队ID = $1"]
        参数列表 = [团队ID]
        参数索引 = 2

        if 状态筛选:
            条件列表.append(f"uta.状态 = ${参数索引}")
            参数列表.append(状态筛选)
            参数索引 += 1

        条件SQL = " AND ".join(条件列表)

        # 获取总数
        计数SQL = f"""
        SELECT COUNT(*) as total FROM 用户团队关联表 uta
        WHERE {条件SQL}
        """
        总数结果 = await 异步连接池实例.执行查询(计数SQL, tuple(参数列表))
        总数 = 总数结果[0]['total'] if 总数结果 else 0

        # 获取分页数据
        查询SQL = f"""
        SELECT
            uta.id as 关联ID, uta.用户ID, uta.团队ID, uta.邀请人ID,
            uta.职位, uta.状态, uta.邀请过期时间, uta.备注,
            uta.加入时间, uta.创建时间, uta.更新时间,
            u1.昵称 as 被邀请人姓名, u1.手机号 as 被邀请人手机,
            u2.昵称 as 邀请人姓名, u2.手机号 as 邀请人手机,
            t.团队名称
        FROM 用户团队关联表 uta
        LEFT JOIN 用户表 u1 ON uta.用户ID = u1.id
        LEFT JOIN 用户表 u2 ON uta.邀请人ID = u2.id
        LEFT JOIN 团队表 t ON uta.团队ID = t.id
        WHERE {条件SQL}
        ORDER BY uta.创建时间 DESC
        LIMIT ${参数索引} OFFSET ${参数索引 + 1}
        """

        偏移量 = (页码 - 1) * 每页数量
        参数列表.extend([每页数量, 偏移量])

        邀请记录列表 = await 异步连接池实例.执行查询(查询SQL, tuple(参数列表))

        # 处理邀请记录，解析备注信息
        处理后的邀请列表 = []
        前端域名 = os.getenv('FRONTEND_URL', 'https://crm.limob.cn')

        for 记录 in 邀请记录列表:
            # 解析备注信息
            备注信息 = Postgre_解析备注信息(记录['备注'] or '')
            邀请令牌 = 备注信息.get('邀请令牌', '')

            # 生成邀请链接
            邀请链接 = f"{前端域名}/invitation?token={邀请令牌}&teamId={记录['团队ID']}" if 邀请令牌 else ""

            邀请记录 = {
                "id": 记录['关联ID'],
                "关联ID": 记录['关联ID'],
                "用户ID": 记录['用户ID'],
                "团队ID": 记录['团队ID'],
                "邀请人ID": 记录['邀请人ID'],
                "被邀请人姓名": 记录['被邀请人姓名'] or "未知",
                "被邀请人手机": 记录['被邀请人手机'] or 备注信息.get('手机号', ''),
                "邀请人姓名": 记录['邀请人姓名'] or "未知",
                "职位": 记录['职位'],
                "状态": 记录['状态'],
                "邀请令牌": 邀请令牌,
                "邀请链接": 邀请链接,
                "邀请过期时间": 记录['邀请过期时间'].isoformat() if 记录['邀请过期时间'] else None,
                "加入时间": 记录['加入时间'].isoformat() if 记录['加入时间'] else None,
                "创建时间": 记录['创建时间'].isoformat() if 记录['创建时间'] else None,
                "更新时间": 记录['更新时间'].isoformat() if 记录['更新时间'] else None,
                "团队名称": 记录['团队名称'],
                "权限列表": 备注信息.get('权限列表', [])
            }

            处理后的邀请列表.append(邀请记录)

        总页数 = (总数 + 每页数量 - 1) // 每页数量

        return {
            "success": True,
            "data": {
                "邀请列表": 处理后的邀请列表,
                "总数": 总数,
                "页码": 页码,
                "每页数量": 每页数量,
                "总页数": 总页数
            }
        }

    except Exception as e:
        错误日志器.error(f"获取团队邀请列表失败: {e}", exc_info=True)
        return {
            "success": False,
            "message": f"获取邀请列表失败：{str(e)}"
        }

async def Postgre_异步通过邀请令牌获取邀请详情(邀请令牌: str) -> Dict[str, Any]:
    """通过邀请令牌获取邀请详情"""
    try:
        数据库日志器.info(f"查找邀请令牌: {邀请令牌}")

        查询SQL = """
        SELECT
            uta.id as 关联ID, uta.用户ID, uta.团队ID, uta.邀请人ID,
            uta.职位, uta.状态, uta.邀请过期时间, uta.备注,
            uta.加入时间, uta.创建时间, uta.更新时间,
            u1.昵称 as 被邀请人姓名, u1.手机号 as 被邀请人手机,
            u2.昵称 as 邀请人姓名, u2.手机号 as 邀请人手机,
            t.团队名称
        FROM 用户团队关联表 uta
        LEFT JOIN 用户表 u1 ON uta.用户ID = u1.id
        LEFT JOIN 用户表 u2 ON uta.邀请人ID = u2.id
        LEFT JOIN 团队表 t ON uta.团队ID = t.id
        WHERE uta.备注 ILIKE $1
        """

        邀请信息结果 = await 异步连接池实例.执行查询(查询SQL, (f'%令牌:{邀请令牌}%',))

        if not 邀请信息结果:
            return {
                "success": False,
                "status": 404,
                "message": "邀请不存在或邀请令牌无效"
            }

        邀请信息 = 邀请信息结果[0]

        # 检查邀请状态
        if 邀请信息['状态'] == INVITATION_STATUS["REJECTED"]:
            return {
                "success": False,
                "status": 410,
                "message": "邀请已被拒绝"
            }

        if 邀请信息['状态'] == INVITATION_STATUS["ACCEPTED"]:
            return {
                "success": False,
                "status": 409,
                "message": "邀请已被接受"
            }

        # 检查是否过期
        if 邀请信息['邀请过期时间'] and datetime.now() > 邀请信息['邀请过期时间']:
            # 更新状态为已过期
            await Postgre_更新邀请状态(邀请信息['关联ID'], INVITATION_STATUS["EXPIRED"])
            return {
                "success": False,
                "status": 410,
                "message": "邀请已过期"
            }

        # 解析备注信息
        备注信息 = Postgre_解析备注信息(邀请信息['备注'] or '')

        return {
            "success": True,
            "status": 100,
            "data": {
                "关联ID": 邀请信息['关联ID'],
                "邀请人ID": 邀请信息['邀请人ID'],
                "邀请人姓名": 邀请信息['邀请人姓名'],
                "团队ID": 邀请信息['团队ID'],
                "团队名称": 邀请信息['团队名称'],
                "职位": 邀请信息['职位'],
                "手机号": 备注信息.get('手机号', ''),
                "权限列表": 备注信息.get('权限列表', []),
                "创建时间": 邀请信息['创建时间'].isoformat() if 邀请信息['创建时间'] else None,
                "过期时间": 邀请信息['邀请过期时间'].isoformat() if 邀请信息['邀请过期时间'] else None,
                "邀请状态": 邀请信息['状态']
            }
        }

    except Exception as e:
        错误日志器.error(f"获取邀请详情失败: {e}", exc_info=True)
        return {
            "success": False,
            "status": 500,
            "message": f"获取邀请详情失败：{str(e)}"
        }

async def Postgre_更新邀请状态(关联ID: int, 新状态: str) -> bool:
    """更新邀请状态"""
    try:
        更新SQL = """
        UPDATE 用户团队关联表 SET
            状态 = $1, 更新时间 = $2
        WHERE id = $3
        """
        await 异步连接池实例.执行更新(更新SQL, (新状态, datetime.now(), 关联ID))

        数据库日志器.info(f"邀请状态更新成功，关联ID: {关联ID}, 新状态: {新状态}")
        return True

    except Exception as e:
        错误日志器.error(f"更新邀请状态失败: {e}", exc_info=True)
        return False

async def Postgre_异步接受邀请(邀请令牌: str, 用户ID: int) -> Dict[str, Any]:
    """接受邀请"""
    try:
        数据库日志器.info(f"处理邀请接受：邀请令牌={邀请令牌}, 用户ID={用户ID}")

        # 查找邀请记录
        查询SQL = """
        SELECT id, 用户ID, 团队ID, 邀请人ID, 职位, 状态, 邀请过期时间, 备注
        FROM 用户团队关联表
        WHERE 备注 ILIKE $1
        """
        邀请记录结果 = await 异步连接池实例.执行查询(查询SQL, (f'%令牌:{邀请令牌}%',))

        if not 邀请记录结果:
            return {
                "success": False,
                "message": "邀请不存在或邀请令牌无效"
            }

        邀请记录 = 邀请记录结果[0]

        # 检查邀请状态
        if 邀请记录['状态'] != INVITATION_STATUS["PENDING"]:
            return {
                "success": False,
                "message": f"邀请状态为{邀请记录['状态']}，无法接受"
            }

        # 检查邀请是否过期
        if 邀请记录['邀请过期时间'] and datetime.now() > 邀请记录['邀请过期时间']:
            return {
                "success": False,
                "message": "邀请已过期，无法接受"
            }

        # 更新邀请记录状态并关联用户
        更新SQL = """
        UPDATE 用户团队关联表 SET
            用户ID = $1, 状态 = $2, 加入时间 = $3, 更新时间 = $4
        WHERE id = $5
        """
        await 异步连接池实例.执行更新(更新SQL, (
            用户ID, INVITATION_STATUS["ACCEPTED"], datetime.now(), datetime.now(), 邀请记录['id']
        ))

        数据库日志器.info(f"邀请接受成功：用户ID={用户ID}, 团队ID={邀请记录['团队ID']}")

        return {
            "success": True,
            "message": "邀请接受成功",
            "data": {
                "团队ID": 邀请记录['团队ID'],
                "职位": 邀请记录['职位']
            }
        }

    except Exception as e:
        错误日志器.error(f"接受邀请失败: {e}", exc_info=True)
        return {
            "success": False,
            "message": f"接受邀请失败：{str(e)}"
        }
