import json
from datetime import datetime
from typing import Optional, Tuple, Dict, Any, List, TYPE_CHECKING

# 导入项目已有的异步数据库连接池和日志记录器
from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例  # 假设这是获取连接池的正确方式
from 日志 import 数据库日志器, 错误日志器  # 假设日志记录器已配置并可用

if TYPE_CHECKING: # 条件导入，避免循环依赖
    from 数据模型.线索模型 import 获取线索列表请求模型

async def 获取或创建联系方式并返回ID(联系方式: str, 联系方式类型: str, 来源: Optional[str] = "用户添加") -> Optional[int]:
    """
    获取或创建联系方式记录，返回联系方式表ID

    参数:
        联系方式: 联系方式内容
        联系方式类型: 联系方式类型，只接受 "微信"、"手机"、"邮箱"
        来源: 记录来源，默认为"用户添加"

    返回:
        联系方式表ID，失败返回None
    """
    # 验证联系方式类型
    允许的类型 = ["微信", "手机", "邮箱"]
    if 联系方式类型 not in 允许的类型:
        错误日志器.error(f"不支持的联系方式类型: {联系方式类型}，只支持: {允许的类型}")
        return None

    try:
        当前时间 = datetime.now()

        # UPSERT SQL 语句 - 如果联系方式和类型组合已存在，返回现有ID；否则插入新记录
        创建_sql_upsert = """
        INSERT INTO kol.联系方式表 (联系方式, 类型, 来源, 创建时间, 更新时间)
        VALUES (%s, %s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE
            更新时间 = VALUES(更新时间),
            id = LAST_INSERT_ID(id)
        """
        插入参数 = (联系方式, 联系方式类型, 来源, 当前时间, 当前时间)

        数据库日志器.debug(f"[获取或创建联系方式] SQL: {创建_sql_upsert} 参数: {插入参数}")

        # 执行UPSERT操作
        结果 = await 异步连接池实例.执行更新(创建_sql_upsert, 插入参数)

        if 结果 and hasattr(结果, 'lastrowid') and 结果.lastrowid:
            联系方式ID = 结果.lastrowid
            数据库日志器.info(f"[获取或创建联系方式] 成功，联系方式ID: {联系方式ID}")
            return 联系方式ID
        else:
            # 如果UPSERT没有返回lastrowid，手动查询ID
            查询SQL = "SELECT id FROM kol.联系方式表 WHERE 联系方式 = %s AND 类型 = %s"
            查询结果 = await 异步连接池实例.执行查询(查询SQL, (联系方式, 联系方式类型))

            if 查询结果:
                联系方式ID = 查询结果[0]["id"]
                数据库日志器.info(f"[获取或创建联系方式] 查询到现有记录，联系方式ID: {联系方式ID}")
                return 联系方式ID
            else:
                错误日志器.error(f"[获取或创建联系方式] 操作失败，无法获取联系方式ID")
                return None

    except Exception as e:
        错误日志器.error(f"[获取或创建联系方式] 操作异常: {str(e)}", exc_info=True)
        return None


async def 获取或创建联系方式并返回完整数据(内容: str, 类型: Optional[str], 记录来源: Optional[str]) -> Optional[Dict[str, Any]]:
    """
    根据联系方式内容和类型查询 kol.联系方式表。
    如果找到匹配记录，则返回其完整数据。
    如果未找到，则创建新记录，并返回新记录的完整数据。
    发生错误则返回 None (或抛出异常，根据实际错误处理策略)。
    此版本采用 UPSERT 机制来处理并发插入的唯一键冲突。
    """
    当前时间 = datetime.now()

    # UPSERT SQL 语句 (MySQL specific)
    # 当 (联系方式, 类型) 发生唯一键冲突时:
    # 1. 更新 '更新时间' 为当前尝试插入的值 (或 NOW())，确保记录是最新的。
    # 2. 使用 id = LAST_INSERT_ID(id) 技巧，使得 connection.insert_id() 或 cursor.lastrowid
    #    能够返回发生冲突的行的 ID，或者新插入行的 ID。
    创建_sql_upsert = """
    INSERT INTO kol.联系方式表 (联系方式, 类型, 来源, 创建时间, 更新时间)
    VALUES (%s, %s, %s, %s, %s)
    ON DUPLICATE KEY UPDATE
        更新时间 = VALUES(更新时间),
        id = LAST_INSERT_ID(id)
    """
    插入参数 = (内容, 类型, 记录来源, 当前时间, 当前时间)

    数据库日志器.debug(f"[获取或创建联系方式并返回完整数据_UPSERT] SQL: {创建_sql_upsert} 参数: {插入参数}")
    
    try:
        # 假设 异步连接池实例.执行插入 返回的是 lastrowid (即受影响行的ID)
        联系方式id = await 异步连接池实例.执行插入(创建_sql_upsert, 插入参数)
        
        if 联系方式id is not None and 联系方式id > 0:
            数据库日志器.info(f"[获取或创建联系方式并返回完整数据_UPSERT] 成功通过UPSERT操作获取联系方式ID: {联系方式id}。准备获取完整记录...")
            # 无论记录是新插入还是已存在（因冲突而"更新"），我们都有了ID
            # 现在根据这个ID获取完整的、最新的记录信息
            联系方式数据 = await 根据id获取联系方式(联系方式id)
            if 联系方式数据:
                数据库日志器.info(f"[获取或创建联系方式并返回完整数据_UPSERT] 成功获取联系方式的完整数据: {联系方式数据}")
                return 联系方式数据
            else:
                # 这种情况理论上不应发生，如果UPSERT成功并返回有效ID，则该ID对应的记录应该存在
                错误日志器.error(f"[获取或创建联系方式并返回完整数据_UPSERT] UPSERT成功并获取ID {联系方式id}后，未能查找到该记录。内容: '{内容}', 类型: '{类型}'")
                return None # 表示数据不一致或存在严重问题
        else:
            # 如果执行插入后没有返回有效的ID，说明UPSERT操作本身失败，或者连接池/驱动未按预期返回ID
            错误日志器.error(f"[获取或创建联系方式并返回完整数据_UPSERT] UPSERT操作后未能获取到有效的ID。可能原因：插入失败、连接问题或驱动未返回ID。内容: '{内容}', 类型: '{类型}'")
            return None

    except Exception as e:
        错误日志器.error(f"[获取或创建联系方式并返回完整数据_UPSERT] 操作失败，内容: '{内容}', 类型: '{类型}'。错误: {str(e)}", exc_info=True)
        # 向上抛出异常，让服务层或全局异常处理器来决定如何响应客户端
        # 例如，可以根据错误类型判断是否需要重试（如数据库死锁）
        raise

async def 根据id获取联系方式(联系方式id: int) -> Optional[Dict]:
    """
    根据联系方式ID从 kol.联系方式表 查询联系方式的完整信息。
    返回包含所有字段的字典，如果未找到则返回 None。
    """
    查询_sql = "SELECT id, 联系方式, 类型, 来源, 创建时间, 更新时间 FROM kol.联系方式表 WHERE id = %s"
    数据库日志器.debug(f"[根据id获取联系方式] 查询SQL: {查询_sql} 参数: ({联系方式id},)")

    try:
        结果 = await 异步连接池实例.执行查询(查询_sql, (联系方式id,))
        if 结果:
            联系方式数据 = 结果[0]
            数据库日志器.info(f"[根据id获取联系方式] 成功获取联系方式详情，ID: {联系方式id}, 数据: {联系方式数据}")
            return 联系方式数据
        else:
            数据库日志器.warning(f"[根据id获取联系方式] 未找到ID为 {联系方式id} 的联系方式记录。")
            return None
    except Exception as e:
        错误日志器.error(f"[根据id获取联系方式] 查询失败，ID: {联系方式id}。错误: {str(e)}", exc_info=True)
        raise # 根据项目统一异常处理规范，可能需要向上抛出自定义异常

async def 创建线索(联系方式id: Optional[int], 信息_json: Optional[str], 线索来源_param: Optional[str], 更新用户_param: int, 创建时间_param: datetime, 更新时间_param: datetime) -> int:
    """
    向 kol.线索表 插入新记录。
    使用传入的创建时间和更新时间。
    返回新创建线索的 id。
    """
    插入_sql = """
    INSERT INTO kol.线索表 (联系方式id, 信息, 线索来源, 更新用户, 创建时间, 更新时间)
    VALUES (%s, %s, %s, %s, %s, %s)
    """
    # 当前时间 = datetime.now() # 不再使用此行，改用参数
    插入参数 = (联系方式id, 信息_json, 线索来源_param, 更新用户_param, 创建时间_param, 更新时间_param)
    
    数据库日志器.debug(f"[创建线索] 插入SQL: {插入_sql} 参数: {插入参数}")
    
    try:
        新线索id = await 异步连接池实例.执行插入(插入_sql, 插入参数)
        if 新线索id is None:
            raise Exception("插入线索失败，未返回线索ID")
        数据库日志器.info(f"[创建线索] 成功创建新线索，关联联系方式ID: {联系方式id}, 更新用户ID: {更新用户_param}, 新线索ID: {新线索id}")
        return 新线索id
    except ConnectionError as conn_error:
        错误日志器.error(f"[创建线索] 数据库连接异常，联系方式ID: {联系方式id}: {str(conn_error)}")
        raise conn_error
    except Exception as e:
        错误日志器.error(f"[创建线索] 插入失败，关联联系方式ID: {联系方式id}, 更新用户ID: {更新用户_param}。错误: {str(e)}", exc_info=True)
        raise # 根据项目统一异常处理规范，可能需要向上抛出自定义异常

async def 根据id获取线索(线索id: int) -> Optional[Dict]:
    """
    根据线索ID从 kol.线索表 查询线索的完整信息。
    返回包含所有原始字段的字典，如果未找到则返回 None。
    """
    查询_sql = "SELECT id, 联系方式id, 信息, 线索来源, 更新用户, 创建时间, 更新时间 FROM kol.线索表 WHERE id = %s"
    数据库日志器.debug(f"[根据id获取线索] 查询SQL: {查询_sql} 参数: ({线索id},)")

    try:
        结果 = await 异步连接池实例.执行查询(查询_sql, (线索id,))
        if 结果:
            线索数据 = 结果[0]
            数据库日志器.info(f"[根据id获取线索] 成功获取线索详情，ID: {线索id}, 数据: {线索数据}")
            return 线索数据
        else:
            数据库日志器.warning(f"[根据id获取线索] 未找到ID为 {线索id} 的线索记录。")
            return None
    except Exception as e:
        错误日志器.error(f"[根据id获取线索] 查询失败，ID: {线索id}。错误: {str(e)}", exc_info=True)
        raise # 根据项目统一异常处理规范，可能需要向上抛出自定义异常

async def 更新线索数据(线索id: int, 更新数据: Dict[str, Any]) -> bool:
    """
    根据线索ID更新 kol.线索表中的记录。
    更新数据字典应包含数据库列名作为键。
    总是会自动更新 '更新时间' 字段。
    返回操作是否成功 (影响的行数是否大于0)。
    """
    if not 更新数据:
        数据库日志器.warning(f"[更新线索数据] 接收到空的更新数据字典，线索ID: {线索id}。不执行更新操作。")
        return False # 或者 True，取决于业务逻辑定义空更新是否算成功

    更新字段列表 = []
    更新值列表 = []

    for 字段, 值 in 更新数据.items():
        # 这里可以添加一个字段名白名单校验，确保只更新允许的字段
        # 例如: if 字段 not in ['联系方式id', '信息', '线索来源', '更新用户']:
        #           continue
        更新字段列表.append(f"{字段} = %s")
        更新值列表.append(值)
    
    # 确保更新时间总是被更新
    if "更新时间" not in 更新数据:
        更新字段列表.append("更新时间 = %s")
        更新值列表.append(datetime.now())
    else: # 如果外部传入了更新时间，确保它是datetime对象
        if isinstance(更新数据["更新时间"], datetime):
             # 已经在循环中添加，这里不需要额外操作
             pass
        else:
            # 如果传入的不是datetime，则使用当前时间覆盖，或根据业务需求决定是否报错
            # 为了简化，这里用当前时间覆盖
            for i, field_expr in enumerate(更新字段列表):
                if field_expr.startswith("更新时间"):
                    更新值列表[i] = datetime.now()
                    break
            else: # 如果循环中没有更新时间，则添加
                 更新字段列表.append("更新时间 = %s")
                 更新值列表.append(datetime.now())

    if not 更新字段列表: # 再次检查，万一只有更新时间且也被过滤了（虽然不太可能）
        数据库日志器.warning(f"[更新线索数据] 没有有效字段可更新，线索ID: {线索id}。")
        return False

    更新语句 = f"UPDATE kol.线索表 SET {', '.join(更新字段列表)} WHERE id = %s"
    更新值列表.append(线索id) # 添加 WHERE 条件中的 id

    数据库日志器.debug(f"[更新线索数据] 更新SQL: {更新语句} 参数: {tuple(更新值列表)}")

    try:
        影响行数 = await 异步连接池实例.执行更新(更新语句, tuple(更新值列表))
        if 影响行数 > 0:
            数据库日志器.info(f"[更新线索数据] 成功更新线索ID: {线索id}。影响行数: {影响行数}。更新内容: {更新数据}")
            return True
        else:
            数据库日志器.warning(f"[更新线索数据] 更新线索ID: {线索id} 未产生影响（可能数据未变化或记录不存在）。更新内容: {更新数据}")
            # 根据业务需求，如果记录不存在或者数据未变化导致影响行数为0，是否算作操作失败
            # 通常，如果记录应存在但未更新，可能需要进一步检查
            return False 
    except Exception as e:
        错误日志器.error(f"[更新线索数据] 更新失败，线索ID: {线索id}。更新内容: {更新数据}。错误: {str(e)}", exc_info=True)
        raise # 根据项目统一异常处理规范，可能需要向上抛出自定义异常

async def 分页获取线索列表(
    页码: int, 
    每页数量: int, 
    筛选条件: "Optional[获取线索列表请求模型]" = None # 类型提示改为字符串形式
) -> Tuple[List[Dict[str, Any]], int]:
    """
    分页获取线索列表，支持动态筛选和联表查询联系方式。
    返回: 元组 (记录列表, 总记录数)
    """
    查询参数列表 = []
    where_子句列表 = []

    # 基础查询语句，联结联系方式表以获取联系方式内容
    # 注意：如果联系方式id可能为NULL，LEFT JOIN是合适的
    # 选择需要的字段，避免 SELECT *
    select_主体 = """
    SELECT 
        cl.id, cl.联系方式id, cl.信息, cl.线索来源, cl.更新用户, cl.创建时间, cl.更新时间,
        cf.联系方式 AS 关联_联系方式内容, 
        cf.类型 AS 关联_联系方式类型,
        cf.来源 AS 关联_联系方式来源 
    FROM kol.线索表 cl
    LEFT JOIN kol.联系方式表 cf ON cl.联系方式id = cf.id
    """
    
    count_select_主体 = "SELECT COUNT(cl.id) as 总数 FROM kol.线索表 cl LEFT JOIN kol.联系方式表 cf ON cl.联系方式id = cf.id"

    if 筛选条件:
        # 处理关键词搜索：如果同时有联系方式和信息值搜索，使用OR逻辑
        关键词搜索条件 = []
        if 筛选条件.筛选_联系方式 and 筛选条件.筛选_信息值 and 筛选条件.筛选_联系方式 == 筛选条件.筛选_信息值:
            # 同一个关键词在联系方式和信息中搜索，使用OR逻辑
            关键词搜索条件.append("(cf.联系方式 LIKE %s OR CAST(cl.信息 AS CHAR) LIKE %s)")
            查询参数列表.extend([f"%{筛选条件.筛选_联系方式}%", f"%{筛选条件.筛选_信息值}%"])
            数据库日志器.info(f"[分页获取线索列表] 添加关键词搜索（OR逻辑）: {筛选条件.筛选_联系方式}")
        else:
            # 分别处理联系方式和信息值搜索
            if 筛选条件.筛选_联系方式:
                关键词搜索条件.append("cf.联系方式 LIKE %s")
                查询参数列表.append(f"%{筛选条件.筛选_联系方式}%")
                数据库日志器.info(f"[分页获取线索列表] 添加联系方式筛选: {筛选条件.筛选_联系方式}")

            if hasattr(筛选条件, '筛选_信息值') and 筛选条件.筛选_信息值:
                关键词搜索条件.append("CAST(cl.信息 AS CHAR) LIKE %s")
                查询参数列表.append(f"%{筛选条件.筛选_信息值}%")
                数据库日志器.info(f"[分页获取线索列表] 添加JSON信息值筛选: {筛选条件.筛选_信息值}")

        if 关键词搜索条件:
            where_子句列表.extend(关键词搜索条件)

        # 其他筛选条件
        if 筛选条件.筛选_线索来源:
            where_子句列表.append("cl.线索来源 = %s")
            查询参数列表.append(筛选条件.筛选_线索来源)

        if hasattr(筛选条件, '起始id') and 筛选条件.起始id is not None and 筛选条件.起始id > 0:
            where_子句列表.append("cl.id > %s")
            查询参数列表.append(筛选条件.起始id)
    
    where_子句_str = ""
    if where_子句列表:
        where_子句_str = " WHERE " + " AND ".join(where_子句列表)
    
    # 构建获取总数的SQL
    获取总数_sql = count_select_主体 + where_子句_str
    数据库日志器.debug(f"[分页获取线索列表] 总数SQL: {获取总数_sql} 参数: {tuple(查询参数列表)}")

    # 构建获取列表的SQL
    # 通常按更新时间或创建时间降序排列
    分页_sql = select_主体 + where_子句_str + " ORDER BY cl.更新时间 DESC LIMIT %s OFFSET %s"
    分页参数 = 查询参数列表[:] # 复制一份用于分页查询
    分页参数.append(每页数量)
    分页参数.append((页码 - 1) * 每页数量) # 计算OFFSET
    数据库日志器.debug(f"[分页获取线索列表] 分页SQL: {分页_sql} 参数: {tuple(分页参数)}")

    try:
        # 获取总记录数
        总数结果 = await 异步连接池实例.执行查询(获取总数_sql, tuple(查询参数列表))
        总记录数 = 总数结果[0]['总数'] if 总数结果 and 总数结果[0] else 0
        数据库日志器.info(f"[分页获取线索列表] 查询到总记录数: {总记录数}，筛选条件: {筛选条件}")

        if 总记录数 == 0:
            return [], 0
            
        # 获取分页数据
        记录列表 = await 异步连接池实例.执行查询(分页_sql, tuple(分页参数))
        数据库日志器.info(f"[分页获取线索列表] 成功获取分页数据，页码: {页码}, 每页数量: {每页数量}, 获取到 {len(记录列表)} 条记录")
        
        return 记录列表, 总记录数
        
    except Exception as e:
        错误日志器.error(f"[分页获取线索列表] 查询失败。页码: {页码}, 每页: {每页数量}, 筛选: {筛选条件}。错误: {str(e)}", exc_info=True)
        raise

async def 检查线索是否存在(联系方式id: Optional[int], 信息_json_str: Optional[str]) -> bool:
    """
    根据联系方式id和序列化后的信息JSON字符串检查线索是否已存在。
    如果 信息_json_str 为 None，则检查是否存在联系方式id匹配且信息为NULL或空对象的线索。
    返回 True 如果存在，否则 False。
    """
    # query = "SELECT id FROM kol.线索表 WHERE 联系方式id = %s AND 信息 = %s" # 旧的查询
    query_base = "SELECT id FROM kol.线索表 WHERE "
    params = []
    conditions = []

    if 联系方式id is not None:
        conditions.append("联系方式id = $1")
        params.append(联系方式id)
    else: # 如果联系方式id是None，理论上应该查不到，或者根据业务定义处理
        # 为了安全，如果外部允许传None，这里可以定义为 联系方式id IS NULL
        conditions.append("联系方式id IS NULL") 

    if 信息_json_str is not None:
        # conditions.append("信息 = %s") # 旧的条件
        # params.append(信息_json_str)
        conditions.append("信息 <=> CAST(%s AS JSON)") # 新的条件，使用NULL-safe等于比较，并将字符串转为JSON类型比较
        params.append(信息_json_str)
    else:
        # 如果传入的 信息_json_str 是 None，表示我们想查找那些 信息 字段本身就是 NULL 或者是一个空JSON对象 (如'{}')的记录
        conditions.append("(信息 IS NULL OR 信息 = JSON_OBJECT() OR 信息 = JSON_ARRAY())") # 覆盖更多空JSON情况
    
    query = query_base + " AND ".join(conditions)
    
    # 日志中截断过长的JSON，避免日志过大
    log_info_json = 信息_json_str[:200] + '...' if 信息_json_str and len(信息_json_str) > 200 else 信息_json_str
    数据库日志器.debug(f"[检查线索是否存在] 查询SQL: {query} 参数: ({联系方式id}, {log_info_json})")

    try:
        结果 = await 异步连接池实例.执行查询(query, tuple(params))
        if 结果 and len(结果) > 0:
            数据库日志器.info(f"[检查线索是否存在] 找到已存在线索，联系方式ID: {联系方式id}, 匹配信息: {log_info_json}。结果: {结果}")
            return True
        else:
            数据库日志器.info(f"[检查线索是否存在] 未找到匹配线索，联系方式ID: {联系方式id}, 匹配信息: {log_info_json}。")
            return False
    except ConnectionError as conn_error:
        错误日志器.error(f"[检查线索是否存在] 数据库连接异常，联系方式ID: {联系方式id}: {str(conn_error)}")
        raise conn_error
    except Exception as e:
        错误日志器.error(f"[检查线索是否存在] 查询失败，联系方式ID: {联系方式id}, 信息: {log_info_json}。错误: {str(e)}", exc_info=True)
        raise

async def 检查线索是否存在_条件化(联系方式id: int, 额外信息: Dict[str, Any], 检查名称: bool) -> Optional[int]:
    """
    根据条件检查线索是否已存在。
    如果存在，返回线索的 ID，否则返回 None。

    参数:
    - 联系方式id: 关联的联系方式记录的ID。
    - 额外信息: 线索的附加信息字典，将作为JSON存储。
    - 检查名称: 布尔值。
        - True: 检查 `额外信息` 中是否存在 "名称" 键。
            - 如果 "名称" 存在: 根据 `联系方式id` 和 `额外信息`中的 "名称" 值判断重复。
            - 如果 "名称" 不存在: 回退到完整 `额外信息` JSON 匹配。
        - False: 始终根据 `联系方式id` 和完整 `额外信息` JSON 匹配。
    """
    params: List[Any] = [联系方式id]  # 修复类型注解，支持混合类型
    query_base = "SELECT id FROM kol.线索表 WHERE 联系方式id = %s AND "

    名称值 = 额外信息.get("名称") if isinstance(额外信息, dict) else None

    if 检查名称 and 名称值 is not None:
        # 按 联系方式id 和 JSON_UNQUOTE(JSON_EXTRACT(信息, '$.名称')) 检查
        query_condition = "JSON_UNQUOTE(JSON_EXTRACT(信息, '$.\"名称\"')) = %s"
        params.append(str(名称值)) # 确保比较的是字符串
        log_message_detail = f"名称: '{名称值}'"
    else:
        # 按 联系方式id 和 完整信息JSON 检查
        信息_json_str = json.dumps(额外信息, ensure_ascii=False, sort_keys=True) if 额外信息 is not None else None
        query_condition = "信息 <=> CAST(%s AS JSON)"
        if 信息_json_str is None:
            query_condition = "信息 IS NULL"
        else:
            params.append(信息_json_str)
        
        log_message_detail = f"完整信息JSON: '{信息_json_str[:200] + '...' if 信息_json_str and len(信息_json_str) > 200 else 信息_json_str}' (检查名称模式: {检查名称}, 实际名称值: {名称值})"

    final_query = query_base + query_condition + " LIMIT 1"
    数据库日志器.debug(f"[检查线索是否存在_条件化] 查询SQL: {final_query} 参数: {tuple(params)}。详情: {log_message_detail}")

    try:
        结果 = await 异步连接池实例.执行查询(final_query, tuple(params))
        if 结果 and len(结果) > 0:
            存在的线索id = 结果[0]['id']
            数据库日志器.info(f"[检查线索是否存在_条件化] 找到已存在线索，ID: {存在的线索id}。联系方式ID: {联系方式id}, 条件详情: {log_message_detail}")
            return 存在的线索id
        else:
            数据库日志器.info(f"[检查线索是否存在_条件化] 未找到匹配线索。联系方式ID: {联系方式id}, 条件详情: {log_message_detail}")
            return None
    except ConnectionError as conn_error:
        错误日志器.error(f"[检查线索是否存在_条件化] 数据库连接异常，联系方式ID: {联系方式id}: {str(conn_error)}")
        raise conn_error
    except Exception as e:
        错误日志器.error(f"[检查线索是否存在_条件化] 查询失败，联系方式ID: {联系方式id}, 条件详情: {log_message_detail}。错误: {str(e)}", exc_info=True)
        raise

# 后续其他数据操作函数将在此添加... 