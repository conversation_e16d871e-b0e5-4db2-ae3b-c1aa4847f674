"""
PostgreSQL异步数据库函数
基于asyncpg实现的高性能PostgreSQL数据库操作函数

特性：
1. 使用asyncpg驱动，性能优异
2. PostgreSQL原生语法，充分利用数据库特性
3. 使用$1, $2参数占位符，防止SQL注入
4. 支持JSONB、数组、枚举等PostgreSQL特性
5. 统一的错误处理和日志记录
"""

import asyncio
import secrets
import time
import traceback
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

from fastapi import Request

import 状态
from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例

# 导入统一日志系统
from 日志 import 安全日志器, 数据库日志器, 错误日志器

# =============================================================================
# 用户相关数据库操作函数
# =============================================================================


async def Postgre_异步获取用户_电话(电话: str) -> Optional[Dict[str, Any]]:
    """
    异步查询用户信息（包含状态字段）

    Args:
        电话: 用户手机号

    Returns:
        用户信息字典或None

    Raises:
        ConnectionError: 数据库连接异常时抛出，供上层认证系统处理
    """
    try:
        结果 = await 异步连接池实例.执行查询(
            "SELECT id, 昵称, password, 手机号, 状态 FROM 用户表 WHERE 手机号 = $1",
            (电话,),
        )
        return 结果[0] if 结果 else None
    except ConnectionError as conn_error:
        # 连接错误，重新抛出以便上层认证系统处理
        错误日志器.error(
            f"查询用户信息时数据库连接异常 (电话: {电话}): {str(conn_error)}"
        )
        raise conn_error
    except Exception as e:
        错误日志器.error(f"查询用户信息异常 (电话: {电话}): {str(e)}", exc_info=True)
        return None


async def Postgre_异步获取用户信息_通过ID(user_id: int) -> Optional[Dict[str, Any]]:
    """
    异步根据用户ID获取用户信息

    Args:
        user_id: 用户ID

    Returns:
        用户信息字典或None
    """
    try:
        结果 = await 异步连接池实例.执行查询(
            "SELECT * FROM 用户表 WHERE id = $1", (user_id,)
        )
        return 结果[0] if 结果 else None
    except Exception as e:
        错误日志器.error(f"根据ID查询用户信息异常 (ID: {user_id}): {str(e)}")
        return None


async def Postgre_异步检查用户是否存在(user_id: int) -> bool:
    """
    异步检查用户是否存在

    Args:
        user_id: 用户ID

    Returns:
        bool: 用户是否存在
    """
    try:
        结果 = await 异步连接池实例.执行查询(
            "SELECT id FROM 用户表 WHERE id = $1", (user_id,)
        )
        return bool(结果)
    except Exception as e:
        错误日志器.error(f"检查用户是否存在异常 (ID: {user_id}): {str(e)}")
        return False


async def Postgre_异步检查手机号是否已注册(电话: str) -> bool:
    """
    异步检查手机号是否已注册（排除未注册状态）

    Args:
        电话: 手机号

    Returns:
        bool: 是否已注册
    """
    try:
        结果 = await 异步连接池实例.执行查询(
            "SELECT id FROM 用户表 WHERE 手机号 = $1 AND (状态 IS NULL OR 状态 != '未注册')",
            (电话,),
        )
        return bool(结果)
    except Exception as e:
        错误日志器.error(f"检查手机号是否已注册异常 (电话: {电话}): {str(e)}")
        return False


async def Postgre_异步获取用户状态_通过手机号(电话: str) -> Optional[str]:
    """异步获取用户状态"""
    try:
        结果 = await 异步连接池实例.执行查询(
            "SELECT id, 状态 FROM 用户表 WHERE 手机号 = $1", (电话,)
        )
        if not 结果:
            return None
        
        用户信息 = 结果[0]
        return 用户信息.get("状态")
    except Exception as e:
        错误日志器.error(f"获取用户状态异常 (电话: {电话}): {str(e)}")
        return None


async def Postgre_异步创建用户_手机号密码(phone: str, hashed_password: str) -> Optional[int]:
    """
    异步创建新用户（手机号+密码）

    Args:
        phone: 手机号
        hashed_password: 加密后的密码

    Returns:
        新用户ID或None
    """
    try:
        用户ID = await 异步连接池实例.执行插入并返回ID(
            "INSERT INTO 用户表 (手机号, password) VALUES ($1, $2)",
            (phone, hashed_password),
        )
        
        if 用户ID:
            数据库日志器.info(f"成功创建新用户 (手机号: {phone}, ID: {用户ID})")
            return 用户ID
        else:
            错误日志器.error(f"创建用户失败，未返回用户ID (手机号: {phone})")
            return None
            
    except Exception as e:
        错误日志器.error(f"创建用户异常 (手机号: {phone}): {str(e)}")
        return None


async def Postgre_异步创建未注册用户(phone: str) -> Optional[int]:
    """
    异步创建未注册用户记录

    Args:
        phone: 手机号

    Returns:
        新用户ID或None
    """
    try:
        # 检查是否已存在该手机号的记录
        现有用户 = await 异步连接池实例.执行查询(
            "SELECT id, 状态 FROM 用户表 WHERE 手机号 = $1", (phone,)
        )
        
        if 现有用户:
            用户信息 = 现有用户[0]
            if 用户信息.get("状态") == "未注册":
                数据库日志器.info(f"手机号 {phone} 已存在未注册记录，返回现有ID: {用户信息['id']}")
                return 用户信息["id"]
            else:
                数据库日志器.warning(f"手机号 {phone} 已存在且状态为: {用户信息.get('状态')}")
                return None

        # 执行插入操作
        用户ID = await 异步连接池实例.执行插入并返回ID(
            "INSERT INTO 用户表 (手机号, 状态) VALUES ($1, $2)", (phone, "未注册")
        )

        if 用户ID:
            数据库日志器.info(f"成功创建未注册用户 (手机号: {phone}, ID: {用户ID})")
            return 用户ID
        else:
            错误日志器.error(f"创建未注册用户失败，未返回用户ID (手机号: {phone})")
            return None

    except Exception as e:
        错误日志器.error(f"创建未注册用户异常 (手机号: {phone}): {str(e)}")
        return None


# =============================================================================
# 用户权限相关函数
# =============================================================================


async def Postgre_异步获取用户有效会员信息(user_id: int) -> Optional[Dict[str, Any]]:
    """
    异步获取用户当前有效的会员信息

    Args:
        user_id: 用户ID

    Returns:
        会员信息字典或None
    """
    try:
        结果 = await 异步连接池实例.执行查询(
            """
            SELECT um.*, m.会员名称, m.每月费用, m.每年费用, m.每月算力点
            FROM 用户_会员_关联表 um
            JOIN 会员表 m ON um.会员id = m.id
            WHERE um.用户id = $1
                AND um.到期时间::timestamp > CURRENT_TIMESTAMP
            ORDER BY m.id DESC
            LIMIT 1
        """,
            (user_id,),
        )
        return 结果[0] if 结果 else None
    except Exception as e:
        错误日志器.error(f"获取用户有效会员信息异常 (用户ID: {user_id}): {str(e)}")
        return None


async def Postgre_异步获取用户权限列表(user_id: int) -> List[Dict[str, Any]]:
    """
    异步获取用户的所有有效权限

    Args:
        user_id: 用户ID

    Returns:
        权限列表
    """
    try:
        结果 = await 异步连接池实例.执行查询(
            """
            SELECT DISTINCT p.id, p.权限名称, p.权限描述, p.权限类型
            FROM 用户_会员_关联表 AS um
            INNER JOIN 会员_权限_关联表 AS mp ON um.会员id = mp.会员id
            INNER JOIN 权限表 AS p ON mp.权限id = p.id
            WHERE
                um.用户id = $1
                AND um.到期时间::timestamp > CURRENT_TIMESTAMP
        """,
            (user_id,),
        )
        return 结果
    except Exception as e:
        错误日志器.error(f"获取用户权限列表异常 (用户ID: {user_id}): {str(e)}")
        return []


async def Postgre_异步获取用户详细信息_包含权限(用户id: int) -> Optional[Dict[str, Any]]:
    """
    异步获取用户详细信息，包含权限信息

    Args:
        用户id: 用户ID

    Returns:
        包含权限信息的用户详细信息字典或None
    """
    try:
        # 获取用户基本信息
        用户信息_sql = """
        SELECT id, 手机号, 昵称, is_admin 
        FROM 用户表 
        WHERE id = $1
        """
        用户基本信息 = await 异步连接池实例.执行查询(用户信息_sql, (用户id,))
        
        if not 用户基本信息:
            return None
        
        用户信息 = 用户基本信息[0]
        
        # 获取用户当前有效会员信息
        会员信息_sql = """
        SELECT um.*, m.会员名称, m.每月费用, m.每年费用, m.每月算力点
        FROM 用户_会员_关联表 um
        JOIN 会员表 m ON um.会员id = m.id
        WHERE um.用户id = $1
            AND um.到期时间::timestamp > CURRENT_TIMESTAMP
        ORDER BY m.id DESC
        LIMIT 1
        """
        会员信息 = await 异步连接池实例.执行查询(会员信息_sql, (用户id,))
        
        # 获取用户权限列表
        权限信息_sql = """
        SELECT DISTINCT p.id, p.权限名称, p.权限描述, p.权限类型
        FROM 权限表 AS p
        INNER JOIN 会员_权限_关联表 AS mp ON mp.权限id = p.id
        INNER JOIN 用户_会员_关联表 AS um ON um.会员id = mp.会员id
        WHERE um.用户id = $1
            AND um.到期时间::timestamp > CURRENT_TIMESTAMP
        ORDER BY p.id
        """
        权限列表 = await 异步连接池实例.执行查询(权限信息_sql, (用户id,))
        
        # 组装完整信息
        完整信息 = {
            **用户信息,
            "会员信息": 会员信息[0] if 会员信息 else None,
            "权限列表": 权限列表,
            "权限数量": len(权限列表),
            "是否有效会员": bool(会员信息)
        }
        
        return 完整信息
        
    except Exception as e:
        错误日志器.error(f"获取用户详细信息异常 (用户ID: {用户id}): {str(e)}")
        
        # 返回最基本的用户信息，确保JWT能正常生成
        try:
            基本信息_sql = "SELECT id, 手机号, 昵称, is_admin FROM 用户表 WHERE id = $1"
            基本结果 = await 异步连接池实例.执行查询(基本信息_sql, (用户id,))
            if 基本结果:
                return {
                    **基本结果[0],
                    "会员信息": None,
                    "权限列表": [],
                    "权限数量": 0,
                    "是否有效会员": False
                }
        except Exception as fallback_error:
            错误日志器.error(f"获取基本用户信息也失败 (用户ID: {用户id}): {str(fallback_error)}")
        
        return None


async def Postgre_异步获取用户昵称(用户id: int) -> Optional[str]:
    """异步获取用户昵称"""
    try:
        结果 = await 异步连接池实例.执行查询(
            "SELECT 昵称 FROM 用户表 WHERE id = $1", (用户id,)
        )
        return 结果[0]["昵称"] if 结果 else None
    except Exception as e:
        错误日志器.error(f"获取用户昵称异常 (用户ID: {用户id}): {str(e)}")
        return None


async def Postgre_异步获取用户邀请人ID(用户id: int) -> Optional[int]:
    """异步获取用户的邀请人ID"""
    try:
        sql = "SELECT 邀请人 FROM 用户表 WHERE id = $1"
        结果 = await 异步连接池实例.执行查询(sql, (用户id,))
        if 结果 and 结果[0] and 结果[0]["邀请人"] is not None:
            return 结果[0]["邀请人"]
        return None
    except Exception as e:
        错误日志器.error(f"获取用户邀请人ID异常 (用户ID: {用户id}): {str(e)}")
        return None


# =============================================================================
# 支付订单相关函数
# =============================================================================


async def Postgre_异步获取支付订单(订单id: int, 用户id: int) -> Optional[Dict[str, Any]]:
    """异步获取支付订单信息"""
    try:
        结果 = await 异步连接池实例.执行查询(
            "SELECT * FROM 支付订单表 WHERE id = $1 AND 用户id = $2", (订单id, 用户id)
        )
        return 结果[0] if 结果 else None
    except Exception as e:
        错误日志器.error(f"获取支付订单异常 (订单ID: {订单id}, 用户ID: {用户id}): {str(e)}")
        return None


async def Postgre_异步获取用户密码(用户id: int) -> Optional[str]:
    """异步获取用户密码"""
    try:
        结果 = await 异步连接池实例.执行查询(
            "SELECT password FROM 用户表 WHERE id = $1", (用户id,)
        )
        return 结果[0]["password"] if 结果 else None
    except Exception as e:
        错误日志器.error(f"获取用户密码异常 (用户ID: {用户id}): {str(e)}")
        return None


async def Postgre_异步更新用户密码(用户id: int, 新密码: str) -> bool:
    """异步更新用户密码"""
    try:
        await 异步连接池实例.执行更新(
            "UPDATE 用户表 SET password = $1 WHERE id = $2", (新密码, 用户id)
        )
        return True
    except Exception as e:
        错误日志器.error(f"更新用户密码异常 (用户ID: {用户id}): {str(e)}")
        return False


# =============================================================================
# 激活码相关函数
# =============================================================================


async def Postgre_异步获取激活码信息(code: str) -> Optional[Dict[str, Any]]:
    """异步获取激活码信息"""
    try:
        结果 = await 异步连接池实例.执行查询(
            """
            SELECT
                a.id, a.激活码, a.激活码类型表id, a.激活用户id, a.使用时间,
                t.类型名称, t.会员id, t.延长天数
            FROM
                激活码表 a
            LEFT JOIN
                激活码类型表 t ON a.激活码类型表id = t.id
            WHERE
                a.激活码 = $1
            """,
            (code,),
        )
        return 结果[0] if 结果 else None
    except Exception as e:
        错误日志器.error(f"获取激活码信息异常 (激活码: {code}): {str(e)}")
        return None


async def Postgre_异步获取激活码类型信息(类型id: int) -> Optional[Dict[str, Any]]:
    """异步获取激活码类型信息"""
    try:
        结果 = await 异步连接池实例.执行查询(
            """
            SELECT
                id, 类型名称, 会员id, 延长天数
            FROM
                激活码类型表
            WHERE
                id = $1
            """,
            (类型id,),
        )
        return 结果[0] if 结果 else None
    except Exception as e:
        错误日志器.error(f"获取激活码类型信息异常 (类型ID: {类型id}): {str(e)}")
        return None


async def Postgre_异步检查用户会员关联(用户id: int, 会员id: int) -> Optional[Dict[str, Any]]:
    """异步检查用户会员关联"""
    try:
        结果 = await 异步连接池实例.执行查询(
            """
            SELECT
                用户id, 会员id, 开通时间, 到期时间
            FROM
                用户_会员_关联表
            WHERE
                用户id = $1 AND 会员id = $2
            """,
            (用户id, 会员id),
        )
        return 结果[0] if 结果 else None
    except Exception as e:
        错误日志器.error(f"检查用户会员关联异常 (用户ID: {用户id}, 会员ID: {会员id}): {str(e)}")
        return None


async def Postgre_异步延长用户会员(用户id: int, 会员id: int, 延长天数: int) -> bool:
    """
    异步延长用户会员时间

    Args:
        用户id: 用户ID
        会员id: 会员ID
        延长天数: 延长的天数

    Returns:
        bool: 是否成功
    """
    try:
        # 检查用户是否已有该会员
        现有关联 = await Postgre_异步检查用户会员关联(用户id, 会员id)

        now = datetime.now()

        if 现有关联:
            # 用户已有该会员，延长时间
            current_expiry_str = 现有关联["到期时间"]

            if current_expiry_str:
                # 解析现有到期时间
                if isinstance(current_expiry_str, datetime):
                    current_expiry = current_expiry_str
                else:
                    current_expiry = datetime.fromisoformat(str(current_expiry_str))

                # 判断当前会员是否过期
                if current_expiry > now:
                    # 未过期，从当前到期时间延长
                    expiry_date = current_expiry + timedelta(days=延长天数)
                    await 异步连接池实例.执行更新(
                        """
                        UPDATE 用户_会员_关联表
                        SET 到期时间 = $1
                        WHERE 用户id = $2 AND 会员id = $3
                        """,
                        (expiry_date.isoformat(), 用户id, 会员id),
                    )
                else:
                    # 已过期，从当前时间开始延长，并更新开通时间
                    expiry_date = now + timedelta(days=延长天数)
                    await 异步连接池实例.执行更新(
                        """
                        UPDATE 用户_会员_关联表
                        SET 开通时间 = $1, 到期时间 = $2
                        WHERE 用户id = $3 AND 会员id = $4
                        """,
                        (
                            now.isoformat(),
                            expiry_date.isoformat(),
                            用户id,
                            会员id,
                        ),
                    )
            else:
                # 到期时间为空，从当前时间开始
                expiry_date = now + timedelta(days=延长天数)
                await 异步连接池实例.执行更新(
                    """
                    UPDATE 用户_会员_关联表
                    SET 开通时间 = $1, 到期时间 = $2
                    WHERE 用户id = $3 AND 会员id = $4
                    """,
                    (
                        now.isoformat(),
                        expiry_date.isoformat(),
                        用户id,
                        会员id,
                    ),
                )
        else:
            # 用户没有该会员，创建新关联
            开通时间 = now.isoformat()
            到期时间 = (now + timedelta(days=延长天数)).isoformat()

            await 异步连接池实例.执行插入(
                """
                INSERT INTO 用户_会员_关联表
                    (用户id, 会员id, 开通时间, 到期时间)
                VALUES
                    ($1, $2, $3, $4)
                """,
                (
                    用户id,
                    会员id,
                    开通时间,
                    到期时间,
                ),
            )

        数据库日志器.info(f"成功延长用户会员 (用户ID: {用户id}, 会员ID: {会员id}, 延长天数: {延长天数})")
        return True

    except Exception as e:
        错误日志器.error(f"延长用户会员异常 (用户ID: {用户id}, 会员ID: {会员id}): {str(e)}")
        return False


async def Postgre_异步使用激活码(用户id: int, 激活码: str) -> bool:
    """
    异步使用激活码

    Args:
        用户id: 用户ID
        激活码: 激活码

    Returns:
        bool: 是否成功
    """
    try:
        # 获取激活码信息
        激活码信息 = await Postgre_异步获取激活码信息(激活码)

        if not 激活码信息:
            错误日志器.warning(f"激活码不存在: {激活码}")
            return False

        # 检查激活码是否已被使用
        if 激活码信息["激活用户id"] is not None:
            错误日志器.warning(f"激活码已被使用: {激活码}, 使用者: {激活码信息['激活用户id']}")
            return False

        # 获取激活码类型信息
        类型信息 = await Postgre_异步获取激活码类型信息(激活码信息["激活码类型表id"])

        if not 类型信息:
            错误日志器.error(f"激活码类型信息不存在: {激活码信息['激活码类型表id']}")
            return False

        # 延长用户会员
        延长成功 = await Postgre_异步延长用户会员(用户id, 类型信息["会员id"], 类型信息["延长天数"])

        if not 延长成功:
            错误日志器.error(f"延长用户会员失败 (用户ID: {用户id}, 会员ID: {类型信息['会员id']})")
            return False

        # 标记激活码为已使用
        now = datetime.now()
        await 异步连接池实例.执行更新(
            """
            UPDATE 激活码表
            SET 激活用户id = $1, 使用时间 = $2
            WHERE id = $3
            """,
            (用户id, now.isoformat(), 激活码信息["id"]),
        )

        数据库日志器.info(f"成功使用激活码 (用户ID: {用户id}, 激活码: {激活码})")
        return True

    except Exception as e:
        错误日志器.error(f"使用激活码异常 (用户ID: {用户id}, 激活码: {激活码}): {str(e)}")
        return False


# =============================================================================
# 用户达人关联相关函数
# =============================================================================


async def Postgre_异步获取用户达人关联(用户id: int, 达人uid: str) -> Optional[Dict[str, Any]]:
    """异步获取用户达人关联信息"""
    try:
        结果 = await 异步连接池实例.执行查询(
            """
            SELECT
                id, 用户ID, 达人UID, 平台, 平台账号, 状态, 创建时间, 更新时间
            FROM
                用户达人关联表
            WHERE
                用户ID = $1 AND 达人UID = $2
            """,
            (用户id, 达人uid),
        )
        return 结果[0] if 结果 else None
    except Exception as e:
        错误日志器.error(f"获取用户达人关联异常 (用户ID: {用户id}, 达人UID: {达人uid}): {str(e)}")
        return None


async def Postgre_异步创建用户达人关联(
    用户id: int,
    达人uid: str,
    平台: str,
    平台账号: str,
    状态: int = 1
) -> Optional[int]:
    """
    异步创建用户达人关联

    Args:
        用户id: 用户ID
        达人uid: 达人UID
        平台: 平台名称
        平台账号: 平台账号
        状态: 状态（默认为1）

    Returns:
        新创建的关联ID或None
    """
    try:
        now = datetime.now()

        结果 = await 异步连接池实例.执行查询(
            """
            INSERT INTO 用户达人关联表
                (用户ID, 达人UID, 平台, 平台账号, 状态, 创建时间, 更新时间)
            VALUES
                ($1, $2, $3, $4, $5, $6, $7)
            RETURNING id
            """,
            (
                用户id,
                达人uid,
                平台,
                平台账号,
                状态,
                now.isoformat(),
                now.isoformat(),
            ),
        )

        if 结果:
            关联ID = 结果[0]["id"]
            数据库日志器.info(f"成功创建用户达人关联 (关联ID: {关联ID}, 用户ID: {用户id}, 达人UID: {达人uid})")
            return 关联ID
        else:
            错误日志器.error("创建用户达人关联失败，未返回ID")
            return None

    except Exception as e:
        错误日志器.error(f"创建用户达人关联异常 (用户ID: {用户id}, 达人UID: {达人uid}): {str(e)}")
        return None


async def Postgre_异步更新用户达人关联状态(关联id: int, 新状态: int) -> bool:
    """异步更新用户达人关联状态"""
    try:
        now = datetime.now()

        影响行数 = await 异步连接池实例.执行更新(
            """
            UPDATE 用户达人关联表
            SET 状态 = $1, 更新时间 = $2
            WHERE id = $3
            """,
            (新状态, now.isoformat(), 关联id),
        )

        if 影响行数 > 0:
            数据库日志器.info(f"成功更新用户达人关联状态 (关联ID: {关联id}, 新状态: {新状态})")
            return True
        else:
            数据库日志器.warning(f"更新用户达人关联状态未影响任何行 (关联ID: {关联id})")
            return False

    except Exception as e:
        错误日志器.error(f"更新用户达人关联状态异常 (关联ID: {关联id}): {str(e)}")
        return False


# =============================================================================
# 用户统计相关函数
# =============================================================================


async def Postgre_异步获取用户统计信息(用户id: int) -> Dict[str, Any]:
    """异步获取用户统计信息"""
    try:
        统计信息 = {
            "用户ID": 用户id,
            "达人关联数": 0,
            "有效会员数": 0,
            "激活码使用数": 0,
        }

        # 统计达人关联数
        达人关联结果 = await 异步连接池实例.执行查询(
            "SELECT COUNT(*) as count FROM 用户达人关联表 WHERE 用户ID = $1 AND 状态 = 1",
            (用户id,)
        )
        if 达人关联结果:
            统计信息["达人关联数"] = 达人关联结果[0]["count"]

        # 统计有效会员数
        会员关联结果 = await 异步连接池实例.执行查询(
            """
            SELECT COUNT(*) as count
            FROM 用户_会员_关联表
            WHERE 用户id = $1 AND 到期时间 > $2
            """,
            (用户id, datetime.now().isoformat())
        )
        if 会员关联结果:
            统计信息["有效会员数"] = 会员关联结果[0]["count"]

        # 统计激活码使用数
        激活码结果 = await 异步连接池实例.执行查询(
            "SELECT COUNT(*) as count FROM 激活码表 WHERE 激活用户id = $1",
            (用户id,)
        )
        if 激活码结果:
            统计信息["激活码使用数"] = 激活码结果[0]["count"]

        数据库日志器.debug(f"获取用户统计信息成功 (用户ID: {用户id})")
        return 统计信息

    except Exception as e:
        错误日志器.error(f"获取用户统计信息异常 (用户ID: {用户id}): {str(e)}")
        return {
            "用户ID": 用户id,
            "达人关联数": 0,
            "有效会员数": 0,
            "激活码使用数": 0,
        }
