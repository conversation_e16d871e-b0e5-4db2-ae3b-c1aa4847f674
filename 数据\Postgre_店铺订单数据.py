"""
PostgreSQL店铺订单数据层
基于asyncpg实现的店铺订单相关数据库操作

特性：
1. 使用PostgreSQL原生语法和特性
2. 支持高效的订单查询和管理
3. 使用$1, $2参数占位符，防止SQL注入
4. 优化的批量操作和事务处理
5. 完整的错误处理和日志记录
"""

from typing import Any, Dict, List, Optional, Tuple

from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 日志 import 数据库日志器, 错误日志器


class Postgre_店铺订单数据层:
    """PostgreSQL店铺订单数据访问层"""

    def __init__(self):
        self.数据库连接池 = 异步连接池实例

        # 安全字段白名单 - 防止SQL注入
        self.允许的字段名 = {
            # 基础订单字段
            "订单id", "商品id", "商品名称", "商品规格", "商品数量", "商品单价", "商品总价",
            "订单状态", "下单时间", "支付时间", "发货时间", "收货时间", "退款时间",
            "买家昵称", "买家手机", "收货地址", "物流公司", "物流单号", "备注",
            "抖音火山号", "用户订单认领表id", "创建时间", "更新时间",
            # 抖音电商字段
            "作者账号", "抖音/火山号", "支付金额", "佣金率", "预估佣金支出", "结算金额",
            "实际佣金支出", "超时未结算原因", "付款时间", "订单结算时间", "商品来源",
            "尾款支付时间", "定金金额", "店铺id", "店铺名称", "佣金发票", "冻结比例",
            "是否阶梯佣金", "门槛销量", "基础佣金率", "升佣佣金率", "预估奖励佣金支出",
            "结算奖励佣金支出", "阶梯计划ID", "支付补贴", "平台补贴", "达人补贴",
            "运费", "税费", "运费补贴", "分销来源", "营销活动id", "推广费率",
            "推广技术服务费", "预估推广费支出", "结算推广费支出", "计划类型",
            "订单来源", "流量细分来源", "流量来源", "订单类型"
        }

    def _验证字段名安全性(self, 字段名: str) -> bool:
        """验证字段名是否安全（防止SQL注入）"""
        if 字段名 not in self.允许的字段名:
            数据库日志器.error(f"检测到不安全的字段名: {字段名}")
            return False

        # 检查是否包含危险字符
        危险字符 = [";", "--", "/*", "*/", "xp_", "sp_", "exec", "execute", "drop", "delete", "update", "insert"]
        字段名_小写 = 字段名.lower()
        for 危险字符 in 危险字符:
            if 危险字符 in 字段名_小写:
                数据库日志器.error(f"字段名包含危险字符: {字段名}")
                return False

        return True

    async def 检查订单是否存在(self, 订单id: int) -> bool:
        """检查订单是否已存在"""
        try:
            检查查询 = "SELECT COUNT(*) as count FROM 店铺订单表 WHERE 订单id = $1"
            检查结果 = await self.数据库连接池.执行查询(检查查询, (订单id,))
            return 检查结果 and 检查结果[0]["count"] > 0
        except Exception as e:
            数据库日志器.error(f"检查订单是否存在失败: {str(e)}")
            return False

    async def 插入订单数据(self, 订单数据: Dict[str, Any]) -> bool:
        """插入订单数据到数据库"""
        try:
            # 安全验证：检查所有字段名
            字段列表 = list(订单数据.keys())
            for 字段名 in 字段列表:
                if not self._验证字段名安全性(字段名):
                    数据库日志器.error(f"插入订单数据失败: 不安全的字段名 {字段名}")
                    return False

            # 构建安全的插入SQL（使用PostgreSQL语法）
            占位符 = ", ".join([f"${i+1}" for i in range(len(字段列表))])
            字段名称 = ", ".join([f'"{字段}"' for 字段 in 字段列表])  # PostgreSQL使用双引号

            插入查询 = f"""
            INSERT INTO 店铺订单表 ({字段名称})
            VALUES ({占位符})
            """

            参数值 = tuple(订单数据.values())
            await self.数据库连接池.执行插入(插入查询, 参数值)
            数据库日志器.debug(f"成功插入订单数据: 订单ID={订单数据.get('订单id')}")
            return True

        except Exception as e:
            数据库日志器.error(f"插入订单数据失败: {str(e)}")
            return False

    async def 获取用户店铺权限(self, 用户ID: int) -> List[int]:
        """获取用户有权限的店铺ID列表"""
        try:
            权限查询 = "SELECT 店铺ID FROM 用户_店铺 WHERE 用户ID = $1"
            权限结果 = await self.数据库连接池.执行查询(权限查询, (用户ID,))
            return [row["店铺ID"] for row in 权限结果] if 权限结果 else []
        except Exception as e:
            数据库日志器.error(f"获取用户店铺权限失败: {str(e)}")
            return []

    async def 查询订单列表(
        self,
        用户ID: int,
        页码: int = 1,
        每页数量: int = 20,
        店铺ID: Optional[int] = None,
        订单状态: Optional[str] = None,
        商品名称: Optional[str] = None,
        开始时间: Optional[str] = None,
        结束时间: Optional[str] = None,
    ) -> Tuple[List[Dict[str, Any]], int]:
        """查询订单列表"""
        try:
            # 构建查询条件
            查询条件 = []
            查询参数 = [用户ID]
            参数索引 = 2

            # 基础查询：只查询用户有权限的店铺订单
            基础查询 = """
            SELECT 
                o.订单id, o.商品id, o.商品名称, o.作者账号, o.支付金额,
                o.佣金率, o.预估佣金支出, o.实际佣金支出, o.订单状态,
                o.付款时间, o.收货时间, o.店铺id, o.店铺名称, o.商品数量, o.创建时间
            FROM 店铺订单表 o
            WHERE EXISTS (
                SELECT 1 FROM 用户_店铺 us
                INNER JOIN 店铺 s ON us.店铺ID = s.id
                WHERE us.用户ID = $1
                AND s.shop_id = o.店铺id
            )
            """

            # 添加筛选条件
            if 店铺ID:
                查询条件.append(f"o.店铺id = ${参数索引}")
                查询参数.append(str(店铺ID))
                参数索引 += 1

            if 订单状态:
                查询条件.append(f"o.订单状态 = ${参数索引}")
                查询参数.append(订单状态)
                参数索引 += 1

            if 商品名称:
                查询条件.append(f"o.商品名称 ILIKE ${参数索引}")
                查询参数.append(f"%{商品名称}%")
                参数索引 += 1

            if 开始时间:
                查询条件.append(f"o.付款时间 >= ${参数索引}")
                查询参数.append(开始时间)
                参数索引 += 1

            if 结束时间:
                查询条件.append(f"o.付款时间 <= ${参数索引}")
                查询参数.append(结束时间)
                参数索引 += 1

            # 组装完整查询
            if 查询条件:
                完整查询 = 基础查询 + " AND " + " AND ".join(查询条件)
            else:
                完整查询 = 基础查询

            # 添加排序
            完整查询 += " ORDER BY o.创建时间 DESC"

            # 计算总数
            计数查询 = f"SELECT COUNT(*) as total FROM ({完整查询}) as temp"
            总数结果 = await self.数据库连接池.执行查询(计数查询, tuple(查询参数))
            总数 = 总数结果[0]["total"] if 总数结果 else 0

            # 分页查询
            偏移量 = (页码 - 1) * 每页数量
            分页查询 = 完整查询 + f" LIMIT ${参数索引} OFFSET ${参数索引 + 1}"
            查询参数.extend([每页数量, 偏移量])

            订单列表 = await self.数据库连接池.执行查询(分页查询, tuple(查询参数))

            数据库日志器.debug(f"查询订单列表成功: 用户ID={用户ID}, 总数={总数}")
            return 订单列表 or [], 总数

        except Exception as e:
            数据库日志器.error(f"查询订单列表失败: {str(e)}")
            return [], 0

    async def 获取订单详情(self, 订单id: int, 用户ID: int) -> Optional[Dict[str, Any]]:
        """获取订单详情"""
        try:
            详情查询 = """
            SELECT o.*
            FROM 店铺订单表 o
            WHERE o.订单id = $1
            AND EXISTS (
                SELECT 1 FROM 用户_店铺 us 
                WHERE us.用户ID = $2 
                AND (us.店铺ID = CAST(o.店铺id AS INTEGER) OR o.店铺id IS NULL)
            )
            """

            详情结果 = await self.数据库连接池.执行查询(详情查询, (订单id, 用户ID))

            if 详情结果:
                数据库日志器.debug(f"获取订单详情成功: 订单ID={订单id}")
                return 详情结果[0]
            else:
                数据库日志器.warning(f"订单不存在或无权限访问: 订单ID={订单id}, 用户ID={用户ID}")
                return None

        except Exception as e:
            数据库日志器.error(f"获取订单详情失败: {str(e)}")
            return None

    async def 获取订单状态选项(self, 用户ID: int) -> List[str]:
        """获取订单状态选项"""
        try:
            状态查询 = """
            SELECT DISTINCT o.订单状态
            FROM 店铺订单表 o
            WHERE EXISTS (
                SELECT 1 FROM 用户_店铺 us
                INNER JOIN 店铺 s ON us.店铺ID = s.id
                WHERE us.用户ID = $1
                AND s.shop_id = o.店铺id
            )
            AND o.订单状态 IS NOT NULL
            AND o.订单状态 != ''
            ORDER BY o.订单状态
            """

            状态结果 = await self.数据库连接池.执行查询(状态查询, (用户ID,))
            状态列表 = [row["订单状态"] for row in 状态结果] if 状态结果 else []

            数据库日志器.debug(f"获取订单状态选项成功: 用户ID={用户ID}, 状态数量={len(状态列表)}")
            return 状态列表

        except Exception as e:
            数据库日志器.error(f"获取订单状态选项失败: {str(e)}")
            return []

    async def 查询订单详情(self, 订单id: str, 用户ID: int) -> Optional[Dict[str, Any]]:
        """查询单个订单的详细信息"""
        try:
            详情查询 = """
            SELECT
                o.*,
                CASE
                    WHEN uc.id IS NOT NULL THEN uc.抖音号
                    ELSE NULL
                END as 关联抖音号,
                CASE
                    WHEN uc.id IS NOT NULL THEN uc.达人表id
                    ELSE NULL
                END as 关联达人表id
            FROM 店铺订单表 o
            LEFT JOIN 用户订单认领表 uc ON o.用户订单认领表id = uc.id
            WHERE CAST(o.订单id AS TEXT) = $1
            AND EXISTS (
                SELECT 1 FROM 用户_店铺 us
                INNER JOIN 店铺 s ON us.店铺ID = s.id
                WHERE us.用户ID = $2
                AND s.shop_id = o.店铺id
            )
            """

            详情结果 = await self.数据库连接池.执行查询(详情查询, (订单id, 用户ID))

            if 详情结果:
                订单详情 = 详情结果[0]
                数据库日志器.debug(f"查询订单详情成功: 订单ID={订单id}, 用户ID={用户ID}")
                return 订单详情
            else:
                数据库日志器.warning(f"订单不存在或无权限访问: 订单ID={订单id}, 用户ID={用户ID}")
                return None

        except Exception as e:
            数据库日志器.error(f"查询订单详情失败: 订单ID={订单id}, 用户ID={用户ID}, 错误={str(e)}")
            return None

    async def 查找达人信息(self, 抖音火山号: str) -> Optional[int]:
        """在kol数据库中查找达人信息"""
        try:
            查找查询 = """
            SELECT id FROM kol.达人表
            WHERE account_douyin = $1
            LIMIT 1
            """

            查找结果 = await self.数据库连接池.执行查询(查找查询, (抖音火山号,))

            if 查找结果:
                达人id = 查找结果[0]["id"]
                数据库日志器.debug(f"找到达人信息: 抖音火山号={抖音火山号}, 达人id={达人id}")
                return 达人id
            else:
                数据库日志器.debug(f"未找到达人信息: 抖音火山号={抖音火山号}")
                return None

        except Exception as e:
            数据库日志器.error(f"查找达人信息失败: {str(e)}")
            return None

    async def 检查用户订单认领记录存在(self, 抖音号: str, 达人表id: int) -> Optional[int]:
        """检查用户订单认领记录是否已存在"""
        try:
            检查查询 = """
            SELECT id FROM 用户订单认领表
            WHERE 抖音号 = $1 AND 达人表id = $2
            LIMIT 1
            """

            检查结果 = await self.数据库连接池.执行查询(检查查询, (抖音号, 达人表id))

            if 检查结果:
                记录id = 检查结果[0]["id"]
                数据库日志器.debug(f"找到现有用户订单认领记录: 抖音号={抖音号}, 达人表id={达人表id}, 记录id={记录id}")
                return 记录id
            else:
                return None

        except Exception as e:
            数据库日志器.error(f"检查用户订单认领记录失败: {str(e)}")
            return None


    async def 检查用户订单认领记录存在_仅抖音号(self, 抖音号: str) -> Optional[int]:
        """仅基于抖音号检查用户订单认领记录是否已存在"""
        try:
            检查查询 = "SELECT id FROM 用户订单认领表 WHERE 抖音号 = $1 LIMIT 1"
            检查结果 = await self.数据库连接池.执行查询(检查查询, (抖音号,))

            if 检查结果:
                记录id = 检查结果[0]["id"]
                数据库日志器.debug(f"找到现有用户订单认领记录（仅抖音号）: 抖音号={抖音号}, 记录id={记录id}")
                return 记录id
            else:
                return None

        except Exception as e:
            数据库日志器.error(f"检查用户订单认领记录失败（仅抖音号）: {str(e)}")
            return None

    async def 插入用户订单认领记录(self, 抖音号: str, 达人表id: int) -> Optional[int]:
        """插入用户订单认领表记录（带重复性检查）"""
        try:
            # 先检查是否已存在
            现有记录id = await self.检查用户订单认领记录存在(抖音号, 达人表id)
            if 现有记录id:
                数据库日志器.debug(f"使用现有用户订单认领记录: id={现有记录id}")
                return 现有记录id

            # 不存在则插入新记录
            插入查询 = """
            INSERT INTO 用户订单认领表 (抖音号, 达人表id)
            VALUES ($1, $2)
            RETURNING id
            """

            插入结果 = await self.数据库连接池.执行查询(插入查询, (抖音号, 达人表id))

            if 插入结果:
                认领记录id = 插入结果[0]["id"]
                数据库日志器.debug(f"插入新用户订单认领记录成功: 抖音号={抖音号}, 达人表id={达人表id}, 认领记录id={认领记录id}")
                return 认领记录id
            else:
                return None

        except Exception as e:
            数据库日志器.error(f"插入用户订单认领记录失败: {str(e)}")
            return None

    async def 创建用户订单认领记录(self, 抖音号: str, 达人表id: int) -> Optional[int]:
        """创建用户订单认领表记录（仅插入新记录，不检查重复）"""
        try:
            插入查询 = """
            INSERT INTO 用户订单认领表 (抖音号, 达人表id)
            VALUES ($1, $2)
            RETURNING id
            """

            插入结果 = await self.数据库连接池.执行查询(插入查询, (抖音号, 达人表id))

            if 插入结果:
                认领记录id = 插入结果[0]["id"]
                数据库日志器.debug(f"创建新用户订单认领记录成功: 抖音号={抖音号}, 达人表id={达人表id}, 认领记录id={认领记录id}")
                return 认领记录id
            else:
                return None

        except Exception as e:
            数据库日志器.error(f"创建用户订单认领记录失败: {str(e)}")
            return None

    async def 创建用户订单认领记录_仅抖音号(self, 抖音号: str) -> Optional[int]:
        """创建用户订单认领表记录（仅抖音号，达人表id为NULL）"""
        try:
            插入查询 = """
            INSERT INTO 用户订单认领表 (抖音号, 达人表id)
            VALUES ($1, NULL)
            RETURNING id
            """

            插入结果 = await self.数据库连接池.执行查询(插入查询, (抖音号,))

            if 插入结果:
                认领记录id = 插入结果[0]["id"]
                数据库日志器.debug(f"创建新用户订单认领记录成功（仅抖音号）: 抖音号={抖音号}, 认领记录id={认领记录id}")
                return 认领记录id
            else:
                return None

        except Exception as e:
            数据库日志器.error(f"创建用户订单认领记录失败（仅抖音号）: {str(e)}")
            return None

    async def 更新订单认领关联(self, 订单id: int, 用户订单认领表id: int) -> bool:
        """更新店铺订单表的用户订单认领表id字段"""
        try:
            更新查询 = """
            UPDATE 店铺订单表
            SET 用户订单认领表id = $1
            WHERE 订单id = $2
            """

            await self.数据库连接池.执行更新(更新查询, (用户订单认领表id, 订单id))
            数据库日志器.debug(f"更新订单认领关联成功: 订单id={订单id}, 用户订单认领表id={用户订单认领表id}")
            return True

        except Exception as e:
            数据库日志器.error(f"更新订单认领关联失败: {str(e)}")
            return False

    async def 更新用户订单认领记录_达人表id(self, 认领记录id: int, 达人表id: int) -> bool:
        """更新用户订单认领表记录的达人表id字段"""
        try:
            更新查询 = """
            UPDATE 用户订单认领表
            SET 达人表id = $1
            WHERE id = $2
            """

            await self.数据库连接池.执行更新(更新查询, (达人表id, 认领记录id))
            数据库日志器.debug(f"更新用户订单认领记录达人表id成功: 认领记录id={认领记录id}, 达人表id={达人表id}")
            return True

        except Exception as e:
            数据库日志器.error(f"更新用户订单认领记录达人表id失败: {str(e)}")
            return False

    async def 验证订单认领关联(self, 订单id: int) -> Dict[str, Any]:
        """验证订单认领关联的正确性（用于调试）"""
        try:
            验证查询 = """
            SELECT
                o.订单id,
                o.抖音火山号 as 订单_抖音火山号,
                o.用户订单认领表id,
                u.抖音号 as 认领表_抖音号,
                u.达人表id as 认领表_达人表id
            FROM 店铺订单表 o
            LEFT JOIN 用户订单认领表 u ON o.用户订单认领表id = u.id
            WHERE o.订单id = $1
            """

            验证结果 = await self.数据库连接池.执行查询(验证查询, (订单id,))

            if 验证结果:
                结果 = 验证结果[0]
                是否匹配 = 结果["订单_抖音火山号"] == 结果["认领表_抖音号"]

                return {
                    "订单id": 结果["订单id"],
                    "订单_抖音火山号": 结果["订单_抖音火山号"],
                    "用户订单认领表id": 结果["用户订单认领表id"],
                    "认领表_抖音号": 结果["认领表_抖音号"],
                    "认领表_达人表id": 结果["认领表_达人表id"],
                    "关联是否正确": 是否匹配,
                }
            else:
                return {"错误": "订单不存在"}

        except Exception as e:
            数据库日志器.error(f"验证订单认领关联失败: {str(e)}")
            return {"错误": str(e)}


    async def 检查用户文件重复导入(self, 用户ID: int, 文件hash: str) -> Optional[Dict[str, Any]]:
        """基于文件hash检查用户是否已经导入过相同的文件"""
        try:
            查询SQL = """
            SELECT 任务ID, id, 任务状态, 进度百分比, 开始时间, 文件路径, 文件名
            FROM 店铺订单_导入记录表
            WHERE 用户ID = $1 AND 文件hash = $2
            AND 开始时间 >= CURRENT_TIMESTAMP - INTERVAL '24 hours'
            AND 任务状态 IN ('进行中', '超时', '已完成', '部分失败')
            ORDER BY 开始时间 DESC
            LIMIT 1
            """

            结果 = await self.数据库连接池.执行查询(查询SQL, (用户ID, 文件hash))

            if 结果:
                记录 = 结果[0]
                数据库日志器.info(f"数据库中发现重复文件: 用户{用户ID}, hash={文件hash[:8]}, 现有任务={记录['任务ID']}")
                return 记录

            return None

        except Exception as e:
            数据库日志器.error(f"检查文件重复导入失败: {str(e)}")
            return None

    async def 创建导入记录(
        self,
        任务ID: str,
        用户ID: int,
        文件名: str,
        文件大小: int,
        文件路径: str = None,
        文件hash: str = None,
    ) -> Optional[int]:
        """创建导入记录"""
        try:
            插入查询 = """
            INSERT INTO 店铺订单_导入记录表 (
                任务ID, 用户ID, 文件名, 文件大小, 文件路径, 文件hash, 当前批次, 已处理行数,
                可续传, 任务状态, 进度百分比
            ) VALUES ($1, $2, $3, $4, $5, $6, 0, 0, $7, '进行中', 0.00)
            RETURNING id
            """

            可续传 = 1 if 文件路径 else 0
            插入结果 = await self.数据库连接池.执行查询(
                插入查询, (任务ID, 用户ID, 文件名, 文件大小, 文件路径, 文件hash, 可续传)
            )

            if 插入结果:
                导入记录ID = 插入结果[0]["id"]
                数据库日志器.info(f"创建导入记录成功: 任务ID={任务ID}, 记录ID={导入记录ID}")
                return 导入记录ID
            else:
                数据库日志器.error(f"创建导入记录后无法获取ID: 任务ID={任务ID}")
                return None

        except Exception as e:
            数据库日志器.error(f"创建导入记录失败: {str(e)}")
            return None

    async def 更新导入记录状态(self, 导入记录ID: int, 状态: str, 进度百分比: float, 错误信息: str = None):
        """更新导入记录状态"""
        try:
            if 错误信息:
                更新查询 = """
                UPDATE 店铺订单_导入记录表
                SET 任务状态 = $1, 进度百分比 = $2, 错误信息 = $3, 更新时间 = CURRENT_TIMESTAMP
                WHERE id = $4
                """
                await self.数据库连接池.执行更新(更新查询, (状态, 进度百分比, 错误信息, 导入记录ID))
            else:
                更新查询 = """
                UPDATE 店铺订单_导入记录表
                SET 任务状态 = $1, 进度百分比 = $2, 更新时间 = CURRENT_TIMESTAMP
                WHERE id = $3
                """
                await self.数据库连接池.执行更新(更新查询, (状态, 进度百分比, 导入记录ID))

        except Exception as e:
            数据库日志器.error(f"更新导入记录状态失败: {str(e)}")

    async def 更新导入记录总行数(self, 导入记录ID: int, 总行数: int):
        """更新导入记录总行数"""
        try:
            更新查询 = """
            UPDATE 店铺订单_导入记录表
            SET 总行数 = $1, 更新时间 = CURRENT_TIMESTAMP
            WHERE id = $2
            """
            await self.数据库连接池.执行更新(更新查询, (总行数, 导入记录ID))

        except Exception as e:
            数据库日志器.error(f"更新导入记录总行数失败: {str(e)}")

    async def 更新导入记录进度(
        self,
        导入记录ID: int,
        成功数量: int,
        失败数量: int,
        跳过数量: int,
        进度百分比: float,
    ):
        """更新导入记录进度 - 增强调试日志"""
        try:
            数据库日志器.info(
                f"[进度更新] 开始更新: ID={导入记录ID}, 成功={成功数量}, 失败={失败数量}, 跳过={跳过数量}, 进度={进度百分比:.2f}%"
            )

            更新查询 = """
            UPDATE 店铺订单_导入记录表
            SET 成功数量 = $1, 失败数量 = $2, 跳过数量 = $3, 进度百分比 = $4, 更新时间 = CURRENT_TIMESTAMP
            WHERE id = $5
            """
            影响行数 = await self.数据库连接池.执行更新(
                更新查询, (成功数量, 失败数量, 跳过数量, 进度百分比, 导入记录ID)
            )

            数据库日志器.info(f"[进度更新] 更新成功: ID={导入记录ID}, 影响行数={影响行数}")

        except Exception as e:
            错误日志器.error(f"[进度更新] 更新失败: ID={导入记录ID}, 错误={str(e)}")
            错误日志器.error(f"[进度更新] 异常类型: {type(e).__name__}, 详情: {repr(e)}")
            raise

    async def 更新批次进度(
        self,
        导入记录ID: int,
        当前批次: int,
        已处理行数: int,
        成功数量: int,
        失败数量: int,
        跳过数量: int,
        进度百分比: float,
    ):
        """更新批次处理进度"""
        try:
            数据库日志器.info(
                f"[批次更新] ID={导入记录ID}, 批次={当前批次}, 已处理={已处理行数}, 成功={成功数量}, 失败={失败数量}, 跳过={跳过数量}"
            )

            更新查询 = """
            UPDATE 店铺订单_导入记录表
            SET 当前批次 = $1, 已处理行数 = $2, 成功数量 = $3, 失败数量 = $4,
                跳过数量 = $5, 进度百分比 = $6, 更新时间 = CURRENT_TIMESTAMP
            WHERE id = $7
            """
            await self.数据库连接池.执行更新(
                更新查询,
                (当前批次, 已处理行数, 成功数量, 失败数量, 跳过数量, 进度百分比, 导入记录ID),
            )

        except Exception as e:
            错误日志器.error(f"更新批次进度失败: {str(e)}")
            raise


    async def 完成导入记录(
        self,
        导入记录ID: int,
        最终状态: str,
        成功数量: int,
        失败数量: int,
        跳过数量: int,
        进度百分比: float,
        错误信息: str = None,
    ):
        """完成导入记录 - 增强调试日志"""
        try:
            数据库日志器.info(
                f"[完成记录] 开始更新: ID={导入记录ID}, 状态={最终状态}, 成功={成功数量}, 失败={失败数量}, 跳过={跳过数量}"
            )

            更新查询 = """
            UPDATE 店铺订单_导入记录表
            SET 任务状态 = $1, 成功数量 = $2, 失败数量 = $3, 跳过数量 = $4,
                进度百分比 = $5, 错误信息 = $6, 完成时间 = CURRENT_TIMESTAMP, 更新时间 = CURRENT_TIMESTAMP
            WHERE id = $7
            """
            影响行数 = await self.数据库连接池.执行更新(
                更新查询,
                (最终状态, 成功数量, 失败数量, 跳过数量, 进度百分比, 错误信息, 导入记录ID),
            )

            数据库日志器.info(f"[完成记录] 更新成功: ID={导入记录ID}, 影响行数={影响行数}")

        except Exception as e:
            错误日志器.error(f"[完成记录] 更新失败: ID={导入记录ID}, 错误={str(e)}")
            错误日志器.error(f"[完成记录] 异常类型: {type(e).__name__}, 详情: {repr(e)}")
            raise

    async def 批量检查订单存在(self, 订单ids: list) -> set:
        """批量检查订单是否存在"""
        try:
            if not 订单ids:
                return set()

            # 构建IN查询（PostgreSQL语法）
            占位符 = ",".join([f"${i+1}" for i in range(len(订单ids))])
            查询语句 = f"""
            SELECT 订单id FROM 店铺订单表
            WHERE 订单id IN ({占位符})
            """

            结果 = await self.数据库连接池.执行查询(查询语句, tuple(订单ids))
            已存在订单集合 = {row["订单id"] for row in 结果} if 结果 else set()

            数据库日志器.debug(f"批量检查订单存在: 检查{len(订单ids)}个，已存在{len(已存在订单集合)}个")
            return 已存在订单集合

        except Exception as e:
            数据库日志器.error(f"批量检查订单存在失败: {str(e)}")
            return set()

    async def 查询导入记录(self, 任务ID: str, 用户ID: int) -> Optional[Dict[str, Any]]:
        """查询导入记录"""
        try:
            查询SQL = """
            SELECT * FROM 店铺订单_导入记录表
            WHERE 任务ID = $1 AND 用户ID = $2
            """

            结果 = await self.数据库连接池.执行查询(查询SQL, (任务ID, 用户ID))

            if 结果:
                return 结果[0]
            else:
                return None

        except Exception as e:
            数据库日志器.error(f"查询导入记录失败: {str(e)}")
            return None

    async def 查询导入记录_通过ID(self, 导入记录ID: int, 用户ID: int) -> Optional[Dict[str, Any]]:
        """通过ID查询导入记录"""
        try:
            查询SQL = """
            SELECT * FROM 店铺订单_导入记录表
            WHERE id = $1 AND 用户ID = $2
            """

            结果 = await self.数据库连接池.执行查询(查询SQL, (导入记录ID, 用户ID))

            if 结果:
                return 结果[0]
            return None

        except Exception as e:
            数据库日志器.error(f"查询导入记录失败: {str(e)}")
            return None

    async def 查询导入记录列表(
        self, 用户ID: int, 页码: int = 1, 每页数量: int = 20, 状态筛选: str = None
    ) -> Tuple[List[Dict[str, Any]], int]:
        """查询用户的导入记录列表"""
        try:
            查询条件 = ["用户ID = $1"]
            查询参数 = [用户ID]
            参数索引 = 2

            if 状态筛选:
                查询条件.append(f"任务状态 = ${参数索引}")
                查询参数.append(状态筛选)
                参数索引 += 1

            基础查询 = f"""
            SELECT id, 任务ID, 文件名, 任务状态, 总行数, 成功数量, 失败数量, 跳过数量,
                   进度百分比, 开始时间, 完成时间, 错误信息, 可续传, 已处理行数, 当前批次
            FROM 店铺订单_导入记录表
            WHERE {' AND '.join(查询条件)}
            ORDER BY 开始时间 DESC
            """

            # 计算总数
            计数查询 = f"SELECT COUNT(*) as total FROM 店铺订单_导入记录表 WHERE {' AND '.join(查询条件)}"
            总数结果 = await self.数据库连接池.执行查询(计数查询, tuple(查询参数))
            总数 = 总数结果[0]["total"] if 总数结果 else 0

            # 分页查询
            偏移量 = (页码 - 1) * 每页数量
            分页查询 = 基础查询 + f" LIMIT ${参数索引} OFFSET ${参数索引 + 1}"
            查询参数.extend([每页数量, 偏移量])

            记录列表 = await self.数据库连接池.执行查询(分页查询, tuple(查询参数))

            数据库日志器.debug(f"查询导入记录列表成功: 用户ID={用户ID}, 总数={总数}")
            return 记录列表 or [], 总数

        except Exception as e:
            数据库日志器.error(f"查询导入记录列表失败: {str(e)}")
            return [], 0

    async def 批量插入订单数据(self, 订单数据列表: list) -> int:
        """批量插入订单数据 - 性能优化（PostgreSQL版本）"""
        if not 订单数据列表:
            return 0

        try:
            # 使用第一条数据的字段来动态构建插入查询
            第一条数据 = 订单数据列表[0]
            字段列表 = list(第一条数据.keys())

            # 安全验证：检查所有字段名
            for 字段名 in 字段列表:
                if not self._验证字段名安全性(字段名):
                    数据库日志器.error(f"批量插入订单数据失败: 不安全的字段名 {字段名}")
                    return 0

            # 构建安全的动态插入查询（PostgreSQL语法）
            字段名称 = ", ".join([f'"{字段}"' for 字段 in 字段列表])
            占位符 = ", ".join([f"${i+1}" for i in range(len(字段列表))])

            插入查询 = f"""
            INSERT INTO 店铺订单表 ({字段名称})
            VALUES ({占位符})
            """

            # 准备批量插入的数据
            插入数据 = []
            for 数据 in 订单数据列表:
                # 按照字段列表的顺序提取数据值
                数据行 = tuple(数据.get(字段) for 字段 in 字段列表)
                插入数据.append(数据行)

            # 批量插入
            await self.数据库连接池.执行数据库批量插入(插入查询, 插入数据)

            数据库日志器.debug(f"批量插入订单数据成功: 插入{len(插入数据)}条记录")
            return len(插入数据)

        except Exception as e:
            数据库日志器.error(f"批量插入订单数据失败: {str(e)}")
            # 记录详细的错误信息用于调试
            if 订单数据列表:
                数据库日志器.error(f"第一条数据字段: {list(订单数据列表[0].keys())}")
            return 0


# 创建全局实例
Postgre_店铺订单数据层实例 = Postgre_店铺订单数据层()
