"""
PostgreSQL达人数据操作
基于asyncpg实现的达人数据访问层

特性：
1. 使用PostgreSQL原生语法和特性
2. 支持高效的文本搜索和模糊匹配
3. 使用$1, $2参数占位符，防止SQL注入
4. 优化的分页查询和排序
5. 智能的搜索策略（UID、抖音号、昵称）
"""

import re
import time
from typing import Any, Dict, List, Optional, Tuple

from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 日志 import 数据库日志器, 错误日志器


def _是UID格式(文本: str) -> bool:
    """检查文本是否为UID格式（纯数字，长度合理）"""
    return 文本.isdigit() and 6 <= len(文本) <= 20


def _是抖音号格式(文本: str) -> bool:
    """检查文本是否为抖音号格式"""
    # 抖音号通常以字母开头，包含字母、数字、下划线、点号
    return bool(re.match(r'^[a-zA-Z][a-zA-Z0-9._-]*$', 文本)) and len(文本) >= 3


async def Postgre_搜索达人(
    关键词: Optional[str] = None,
    uid: Optional[str] = None,
    页码: int = 1,
    每页数量: int = 20,
    排序字段: str = "update_time",
    排序方向: str = "DESC"
) -> Tuple[List[Dict[str, Any]], int]:
    """
    搜索达人信息

    Args:
        关键词: 搜索关键词（支持UID、抖音号、昵称）
        uid: 精确的UID查询
        页码: 页码（从1开始）
        每页数量: 每页记录数
        排序字段: 排序字段
        排序方向: 排序方向（ASC/DESC）

    Returns:
        (达人列表, 总记录数)
    """
    try:
        # 构建查询条件
        内部条件列表 = []
        查询参数 = []
        参数索引 = 1

        # UID精准查询 - 性能最优，优先级最高
        if uid:
            内部条件列表.append(f"k.uid_number = ${参数索引}")
            查询参数.append(uid)
            参数索引 += 1
        # 关键词搜索 - 智能识别关键词类型并使用相应查询策略
        elif 关键词:
            搜索关键词 = 关键词.strip()
            数据库日志器.info(f"搜索关键词: '{搜索关键词}', 长度: {len(搜索关键词)}")
            if len(搜索关键词) > 0:
                # 智能识别关键词类型
                if _是UID格式(搜索关键词):
                    # UID精确查询 - 性能最优
                    数据库日志器.info(f"检测到UID格式，使用精确查询: {搜索关键词}")
                    内部条件列表.append(f"k.uid_number = ${参数索引}")
                    查询参数.append(搜索关键词)
                    参数索引 += 1
                elif _是抖音号格式(搜索关键词):
                    # 抖音号查询 - 精确匹配和前缀匹配
                    数据库日志器.info(f"检测到抖音号格式，使用抖音号查询: {搜索关键词}")
                    搜索条件 = f"(k.account_douyin = ${参数索引} OR k.account_douyin LIKE ${参数索引 + 1})"
                    内部条件列表.append(搜索条件)
                    查询参数.extend([搜索关键词, f"{搜索关键词}%"])
                    参数索引 += 2
                else:
                    # 昵称模糊查询 - 包含匹配
                    数据库日志器.info(f"检测到昵称格式，使用昵称查询: {搜索关键词}")
                    内部条件列表.append(f"k.nickname ILIKE ${参数索引}")
                    查询参数.append(f"%{搜索关键词}%")
                    参数索引 += 1

        # 构建WHERE子句
        where_clause = ""
        if 内部条件列表:
            where_clause = "WHERE " + " AND ".join(内部条件列表)

        # 验证排序字段安全性
        允许的排序字段 = {
            "uid_number", "account_douyin", "nickname", 
            "create_time", "update_time", "id"
        }
        if 排序字段 not in 允许的排序字段:
            排序字段 = "update_time"
        
        if 排序方向.upper() not in ["ASC", "DESC"]:
            排序方向 = "DESC"

        # 查询总数
        计数SQL = f"""
        SELECT COUNT(*) as total 
        FROM kol.达人表 k 
        {where_clause}
        """
        
        总数结果 = await 异步连接池实例.执行查询(计数SQL, tuple(查询参数))
        总数 = 总数结果[0]["total"] if 总数结果 else 0

        # 查询数据
        偏移量 = (页码 - 1) * 每页数量
        查询SQL = f"""
        SELECT 
            k.id, k.uid_number, k.account_douyin, k.nickname,
            k.create_time, k.update_time
        FROM kol.达人表 k
        {where_clause}
        ORDER BY k.{排序字段} {排序方向}
        LIMIT ${参数索引} OFFSET ${参数索引 + 1}
        """
        
        # 添加分页参数
        查询参数.extend([每页数量, 偏移量])
        
        达人列表 = await 异步连接池实例.执行查询(查询SQL, tuple(查询参数))
        
        数据库日志器.info(f"搜索达人完成，关键词: '{关键词}', 总数: {总数}, 返回: {len(达人列表)}")
        return 达人列表, 总数

    except Exception as e:
        错误日志器.error(f"搜索达人异常，关键词: '{关键词}', 错误: {str(e)}", exc_info=True)
        return [], 0


async def Postgre_获取达人详情(达人id: int) -> Optional[Dict[str, Any]]:
    """
    获取达人详细信息

    Args:
        达人id: 达人ID

    Returns:
        达人详细信息或None
    """
    try:
        查询SQL = """
        SELECT 
            id, uid_number, account_douyin, nickname,
            create_time, update_time
        FROM kol.达人表
        WHERE id = $1
        """
        
        结果 = await 异步连接池实例.执行查询(查询SQL, (达人id,))
        
        if 结果:
            数据库日志器.debug(f"获取达人详情成功，达人ID: {达人id}")
            return 结果[0]
        else:
            数据库日志器.warning(f"未找到达人信息，达人ID: {达人id}")
            return None
            
    except Exception as e:
        错误日志器.error(f"获取达人详情异常，达人ID: {达人id}, 错误: {str(e)}")
        return None


async def Postgre_通过UID获取达人(uid_number: str) -> Optional[Dict[str, Any]]:
    """
    通过UID获取达人信息

    Args:
        uid_number: 达人UID

    Returns:
        达人信息或None
    """
    try:
        查询SQL = """
        SELECT 
            id, uid_number, account_douyin, nickname,
            create_time, update_time
        FROM kol.达人表
        WHERE uid_number = $1
        """
        
        结果 = await 异步连接池实例.执行查询(查询SQL, (uid_number,))
        
        if 结果:
            数据库日志器.debug(f"通过UID获取达人成功，UID: {uid_number}")
            return 结果[0]
        else:
            数据库日志器.debug(f"未找到UID对应的达人，UID: {uid_number}")
            return None
            
    except Exception as e:
        错误日志器.error(f"通过UID获取达人异常，UID: {uid_number}, 错误: {str(e)}")
        return None


async def Postgre_创建或更新达人(
    uid_number: str,
    account_douyin: Optional[str] = None,
    nickname: Optional[str] = None
) -> Optional[int]:
    """
    创建或更新达人信息

    Args:
        uid_number: 达人UID
        account_douyin: 抖音账号
        nickname: 昵称

    Returns:
        达人ID或None
    """
    try:
        当前时间戳 = int(time.time())
        
        # 检查达人是否已存在
        现有达人 = await 异步连接池实例.执行查询(
            "SELECT id FROM kol.达人表 WHERE uid_number = $1", (uid_number,)
        )

        if 现有达人:
            # 更新现有达人
            更新SQL = """
            UPDATE kol.达人表
            SET account_douyin = $1, nickname = $2, update_time = $3
            WHERE uid_number = $4
            RETURNING id
            """
            结果 = await 异步连接池实例.执行查询(
                更新SQL,
                (account_douyin, nickname, 当前时间戳, uid_number)
            )
            
            if 结果:
                达人ID = 结果[0]["id"]
                数据库日志器.info(f"更新达人成功，UID: {uid_number}, ID: {达人ID}")
                return 达人ID
        else:
            # 创建新达人
            插入SQL = """
            INSERT INTO kol.达人表 
                (uid_number, account_douyin, nickname, create_time, update_time)
            VALUES
                ($1, $2, $3, $4, $5)
            RETURNING id
            """
            结果 = await 异步连接池实例.执行查询(
                插入SQL,
                (uid_number, account_douyin, nickname, 当前时间戳, 当前时间戳)
            )
            
            if 结果:
                达人ID = 结果[0]["id"]
                数据库日志器.info(f"创建达人成功，UID: {uid_number}, ID: {达人ID}")
                return 达人ID

        return None

    except Exception as e:
        错误日志器.error(f"创建或更新达人异常，UID: {uid_number}, 错误: {str(e)}")
        return None


async def Postgre_批量获取达人信息(uid_list: List[str]) -> List[Dict[str, Any]]:
    """
    批量获取达人信息

    Args:
        uid_list: UID列表

    Returns:
        达人信息列表
    """
    try:
        if not uid_list:
            return []
        
        # 构建IN查询
        占位符 = ",".join([f"${i+1}" for i in range(len(uid_list))])
        查询SQL = f"""
        SELECT 
            id, uid_number, account_douyin, nickname,
            create_time, update_time
        FROM kol.达人表
        WHERE uid_number IN ({占位符})
        """
        
        结果 = await 异步连接池实例.执行查询(查询SQL, tuple(uid_list))
        
        数据库日志器.debug(f"批量获取达人信息成功，请求: {len(uid_list)}, 返回: {len(结果)}")
        return 结果
        
    except Exception as e:
        错误日志器.error(f"批量获取达人信息异常，UID数量: {len(uid_list)}, 错误: {str(e)}")
        return []


async def Postgre_统计达人数量(筛选条件: Optional[Dict[str, Any]] = None) -> int:
    """
    统计达人数量

    Args:
        筛选条件: 筛选条件字典

    Returns:
        达人总数
    """
    try:
        where_条件 = []
        参数列表 = []
        参数索引 = 1
        
        if 筛选条件:
            # 按创建时间筛选
            if 筛选条件.get("创建时间_开始"):
                where_条件.append(f"create_time >= ${参数索引}")
                参数列表.append(筛选条件["创建时间_开始"])
                参数索引 += 1
            
            if 筛选条件.get("创建时间_结束"):
                where_条件.append(f"create_time <= ${参数索引}")
                参数列表.append(筛选条件["创建时间_结束"])
                参数索引 += 1
            
            # 按更新时间筛选
            if 筛选条件.get("更新时间_开始"):
                where_条件.append(f"update_time >= ${参数索引}")
                参数列表.append(筛选条件["更新时间_开始"])
                参数索引 += 1
            
            if 筛选条件.get("更新时间_结束"):
                where_条件.append(f"update_time <= ${参数索引}")
                参数列表.append(筛选条件["更新时间_结束"])
                参数索引 += 1
        
        where_clause = "WHERE " + " AND ".join(where_条件) if where_条件 else ""
        
        计数SQL = f"""
        SELECT COUNT(*) as total 
        FROM kol.达人表 
        {where_clause}
        """
        
        结果 = await 异步连接池实例.执行查询(计数SQL, tuple(参数列表))
        总数 = 结果[0]["total"] if 结果 else 0
        
        数据库日志器.debug(f"统计达人数量完成，总数: {总数}")
        return 总数
        
    except Exception as e:
        错误日志器.error(f"统计达人数量异常: {str(e)}")
        return 0
