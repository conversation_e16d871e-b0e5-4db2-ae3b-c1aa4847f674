"""
PostgreSQL用户数据操作模块
基于asyncpg实现的用户相关数据库操作

功能：
1. 用户创建、查询、更新、删除
2. 用户认证相关操作
3. 用户权限管理
4. 用户统计和搜索
"""

import json
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union

from 数据.Postgre_异步连接池 import Postgre_异步连接池实例
from 日志 import 数据库日志器, 错误日志器


# ==================== 用户基础操作 ====================

async def Postgre_创建用户(手机号: str, 哈希密码: str) -> int:
    """
    创建用户（返回用户ID）

    Args:
        手机号: 用户手机号
        哈希密码: 加密后的密码

    Returns:
        创建的用户ID

    Raises:
        ConnectionError: 数据库连接异常
        Exception: 其他创建异常
    """
    try:
        插入SQL = """
        INSERT INTO 用户表 (手机号, password) 
        VALUES ($1, $2) 
        RETURNING id
        """
        
        async with Postgre_异步连接池实例.获取连接() as 连接:
            结果 = await 连接.fetchrow(插入SQL, 手机号, 哈希密码)
            用户ID = 结果['id'] if 结果 else None
            
            if 用户ID is None:
                raise Exception("创建用户失败，未返回用户ID")
                
            数据库日志器.info(f"用户创建成功: ID={用户ID}, 手机号={手机号}")
            return 用户ID
            
    except Exception as e:
        错误日志器.error(f"创建用户异常 (电话: {手机号}): {str(e)}")
        raise


async def Postgre_创建半注册用户(手机号: str) -> int:
    """
    异步创建半注册用户（仅用于邀请未注册用户）
    创建状态为"未注册"的用户记录，只包含手机号和用户ID
    
    Args:
        手机号: 手机号码，必须是11位数字
        
    Returns:
        int: 创建的用户ID，如果失败则抛出异常
    """
    try:
        插入SQL = """
        INSERT INTO 用户表 (手机号, 状态) 
        VALUES ($1, '未注册') 
        RETURNING id
        """
        
        async with Postgre_异步连接池实例.获取连接() as 连接:
            结果 = await 连接.fetchrow(插入SQL, 手机号)
            用户ID = 结果['id'] if 结果 else None
            
            if 用户ID is None:
                raise Exception("创建半注册用户失败，未返回用户ID")
                
            数据库日志器.info(f"半注册用户创建成功: ID={用户ID}, 手机号={手机号}")
            return 用户ID
            
    except Exception as e:
        错误日志器.error(f"创建半注册用户异常 (电话: {手机号}): {str(e)}")
        raise


async def Postgre_根据手机号获取用户信息(手机号: str) -> Optional[Dict[str, Any]]:
    """
    根据手机号获取用户信息
    
    Args:
        手机号: 用户手机号
        
    Returns:
        用户信息字典，不存在返回None
    """
    try:
        查询SQL = """
        SELECT id, 昵称, 手机号, password, 状态, 权限等级, 每日邀约次数, 
               每日快递查询次数, 算力值, 可创建团队数, 是否自动审核,
               level, experience_points, 邀请人ID, 代理类型ID,
               created_at, updated_at
        FROM 用户表 
        WHERE 手机号 = $1
        """
        
        async with Postgre_异步连接池实例.获取连接() as 连接:
            结果 = await 连接.fetchrow(查询SQL, 手机号)
            
            if 结果:
                用户信息 = dict(结果)
                数据库日志器.debug(f"根据手机号获取用户信息成功: {手机号}")
                return 用户信息
            else:
                数据库日志器.debug(f"手机号对应的用户不存在: {手机号}")
                return None
                
    except Exception as e:
        错误日志器.error(f"根据手机号获取用户信息失败: {str(e)}")
        return None


async def Postgre_根据用户ID获取用户信息(用户ID: int) -> Optional[Dict[str, Any]]:
    """
    根据用户ID获取用户信息
    
    Args:
        用户ID: 用户的唯一标识
        
    Returns:
        用户信息字典，不存在返回None
    """
    try:
        查询SQL = """
        SELECT id, 昵称, 手机号, password, 状态, 权限等级, 每日邀约次数, 
               每日快递查询次数, 算力值, 可创建团队数, 是否自动审核,
               level, experience_points, 邀请人ID, 代理类型ID,
               created_at, updated_at
        FROM 用户表 
        WHERE id = $1
        """
        
        async with Postgre_异步连接池实例.获取连接() as 连接:
            结果 = await 连接.fetchrow(查询SQL, 用户ID)
            
            if 结果:
                用户信息 = dict(结果)
                数据库日志器.debug(f"获取用户信息成功: ID={用户ID}")
                return 用户信息
            else:
                数据库日志器.warning(f"用户不存在: ID={用户ID}")
                return None
                
    except Exception as e:
        错误日志器.error(f"获取用户信息失败: {str(e)}")
        return None


# ==================== 用户权限相关操作 ====================

async def Postgre_获取用户权限_id(用户ID: int) -> Optional[int]:
    """
    获取用户权限等级
    
    Args:
        用户ID: 用户ID
        
    Returns:
        权限等级，用户不存在返回None
    """
    try:
        查询SQL = "SELECT 权限等级 FROM 用户表 WHERE id = $1"
        
        async with Postgre_异步连接池实例.获取连接() as 连接:
            结果 = await 连接.fetchval(查询SQL, 用户ID)
            
            if 结果 is not None:
                数据库日志器.debug(f"获取用户权限成功: ID={用户ID}, 权限等级={结果}")
                return 结果
            else:
                数据库日志器.warning(f"用户不存在或权限为空: ID={用户ID}")
                return None
                
    except Exception as e:
        错误日志器.error(f"获取用户权限失败: {str(e)}")
        return None


async def Postgre_获取JWT认证信息(用户ID: int) -> Optional[Dict[str, Any]]:
    """
    获取JWT认证所需的用户信息
    
    Args:
        用户ID: 用户ID
        
    Returns:
        包含认证信息的字典，用户不存在返回None
    """
    try:
        查询SQL = """
        SELECT id, 昵称, 手机号, 权限等级, 状态
        FROM 用户表 
        WHERE id = $1
        """
        
        async with Postgre_异步连接池实例.获取连接() as 连接:
            结果 = await 连接.fetchrow(查询SQL, 用户ID)
            
            if 结果:
                认证信息 = dict(结果)
                数据库日志器.debug(f"获取JWT认证信息成功: ID={用户ID}")
                return 认证信息
            else:
                数据库日志器.warning(f"用户不存在: ID={用户ID}")
                return None
                
    except Exception as e:
        错误日志器.error(f"获取JWT认证信息失败: {str(e)}")
        return None


# ==================== 用户业务数据操作 ====================

async def Postgre_增加用户邀约次数(用户ID: int, 增加数量: int) -> bool:
    """
    异步增加用户的每日邀约次数。如果原次数为NULL，则视为0再增加。
    
    Args:
        用户ID: 用户ID
        增加数量: 要增加的次数
        
    Returns:
        是否成功
    """
    try:
        # 使用 COALESCE 处理 NULL 值，确保其被视为 0
        更新SQL = """
        UPDATE 用户表 
        SET 每日邀约次数 = COALESCE(每日邀约次数, 0) + $1 
        WHERE id = $2
        """
        
        async with Postgre_异步连接池实例.获取连接() as 连接:
            结果 = await 连接.execute(更新SQL, 增加数量, 用户ID)
            影响行数 = int(结果.split()[-1]) if 结果.startswith('UPDATE') else 0
            
            if 影响行数 > 0:
                数据库日志器.info(f"成功为用户ID {用户ID} 增加 {增加数量} 次每日邀约次数。")
                return True
            else:
                数据库日志器.warning(f"尝试为用户ID {用户ID} 增加邀约次数，但可能用户不存在。影响行数: {影响行数}")
                return False
                
    except Exception as e:
        错误日志器.error(f"异步为用户ID {用户ID} 增加邀约次数异常: {str(e)}")
        return False


async def Postgre_更新用户算力值(用户ID: int, 新算力值: int) -> bool:
    """
    更新用户算力值
    
    Args:
        用户ID: 用户ID
        新算力值: 新的算力值
        
    Returns:
        是否成功
    """
    try:
        更新SQL = "UPDATE 用户表 SET 算力值 = $1 WHERE id = $2"
        
        async with Postgre_异步连接池实例.获取连接() as 连接:
            结果 = await 连接.execute(更新SQL, 新算力值, 用户ID)
            影响行数 = int(结果.split()[-1]) if 结果.startswith('UPDATE') else 0
            
            if 影响行数 > 0:
                数据库日志器.info(f"成功更新用户ID {用户ID} 的算力值为 {新算力值}")
                return True
            else:
                数据库日志器.warning(f"更新用户算力值失败，用户可能不存在: ID={用户ID}")
                return False
                
    except Exception as e:
        错误日志器.error(f"更新用户算力值异常: {str(e)}")
        return False


# ==================== 用户搜索和列表操作 ====================

async def Postgre_获取用户列表(
    页码: int = 1,
    每页数量: int = 20,
    搜索关键词: Optional[str] = None,
    状态筛选: Optional[str] = None,
    权限等级筛选: Optional[int] = None
) -> tuple[List[Dict[str, Any]], int]:
    """
    获取用户列表（支持分页、搜索、筛选）
    
    Args:
        页码: 页码（从1开始）
        每页数量: 每页显示数量
        搜索关键词: 搜索关键词（支持昵称、手机号）
        状态筛选: 状态筛选
        权限等级筛选: 权限等级筛选
        
    Returns:
        (用户列表, 总数量)
    """
    try:
        # 构建WHERE条件
        where_条件 = []
        参数值 = []
        参数索引 = 1
        
        # 搜索条件
        if 搜索关键词:
            if 搜索关键词.isdigit():
                # 数字搜索，优先匹配ID和手机号
                where_条件.append(f"(id = ${参数索引} OR 手机号 LIKE ${参数索引 + 1})")
                参数值.extend([int(搜索关键词), f"%{搜索关键词}%"])
                参数索引 += 2
            else:
                # 文本搜索，匹配昵称
                where_条件.append(f"昵称 ILIKE ${参数索引}")
                参数值.append(f"%{搜索关键词}%")
                参数索引 += 1
        
        # 状态筛选
        if 状态筛选:
            where_条件.append(f"状态 = ${参数索引}")
            参数值.append(状态筛选)
            参数索引 += 1
            
        # 权限等级筛选
        if 权限等级筛选 is not None:
            where_条件.append(f"权限等级 = ${参数索引}")
            参数值.append(权限等级筛选)
            参数索引 += 1
        
        where_clause = "WHERE " + " AND ".join(where_条件) if where_条件 else ""
        
        # 查询总数
        计数SQL = f"SELECT COUNT(*) as total_count FROM 用户表 {where_clause}"
        
        # 查询数据
        偏移量 = (页码 - 1) * 每页数量
        查询SQL = f"""
        SELECT id, 昵称, 手机号, 状态, 权限等级, created_at as registration_time
        FROM 用户表
        {where_clause}
        ORDER BY created_at DESC
        LIMIT ${参数索引} OFFSET ${参数索引 + 1}
        """
        
        async with Postgre_异步连接池实例.获取连接() as 连接:
            # 获取总数
            总数结果 = await 连接.fetchrow(计数SQL, *参数值)
            总数量 = 总数结果['total_count'] if 总数结果 else 0
            
            # 获取数据
            查询参数 = 参数值 + [每页数量, 偏移量]
            用户列表 = await 连接.fetch(查询SQL, *查询参数)
            
            # 转换结果
            结果列表 = []
            for 用户 in 用户列表:
                用户信息 = dict(用户)
                # 处理datetime序列化
                if 用户信息.get("registration_time"):
                    用户信息["registration_time"] = 用户信息["registration_time"].isoformat()
                # 添加默认头像
                用户信息["avatar_url"] = ""
                结果列表.append(用户信息)
            
            数据库日志器.debug(f"用户列表查询完成: 关键词={搜索关键词}, 返回{len(结果列表)}/{总数量}条记录")
            return 结果列表, 总数量
            
    except Exception as e:
        错误日志器.error(f"获取用户列表失败: {str(e)}")
        return [], 0


# ==================== 用户统计操作 ====================

async def Postgre_获取用户总数() -> int:
    """
    获取用户总数
    
    Returns:
        用户总数
    """
    try:
        查询SQL = "SELECT COUNT(*) as total FROM 用户表"
        
        async with Postgre_异步连接池实例.获取连接() as 连接:
            结果 = await 连接.fetchval(查询SQL)
            总数 = 结果 if 结果 else 0
            
            数据库日志器.debug(f"获取用户总数: {总数}")
            return 总数
            
    except Exception as e:
        错误日志器.error(f"获取用户总数失败: {str(e)}")
        return 0


async def Postgre_获取今日新增用户数() -> int:
    """
    获取今日新增用户数
    
    Returns:
        今日新增用户数
    """
    try:
        查询SQL = """
        SELECT COUNT(*) as today_count 
        FROM 用户表 
        WHERE DATE(created_at) = CURRENT_DATE
        """
        
        async with Postgre_异步连接池实例.获取连接() as 连接:
            结果 = await 连接.fetchval(查询SQL)
            今日新增 = 结果 if 结果 else 0
            
            数据库日志器.debug(f"获取今日新增用户数: {今日新增}")
            return 今日新增
            
    except Exception as e:
        错误日志器.error(f"获取今日新增用户数失败: {str(e)}")
        return 0


# ==================== 用户验证和检查操作 ====================

async def Postgre_用户是否存在_电话(手机号: str) -> bool:
    """
    检查手机号对应的用户是否存在

    Args:
        手机号: 用户手机号

    Returns:
        用户是否存在
    """
    try:
        用户信息 = await Postgre_根据手机号获取用户信息(手机号)
        return 用户信息 is not None
    except Exception as e:
        错误日志器.error(f"检查用户是否存在失败: {str(e)}")
        return False


async def Postgre_用户是否已注册_电话(手机号: str) -> bool:
    """
    检查手机号对应的用户是否已完成注册（状态不为"未注册"）

    Args:
        手机号: 用户手机号

    Returns:
        用户是否已注册
    """
    try:
        用户信息 = await Postgre_根据手机号获取用户信息(手机号)
        if not 用户信息:
            return False
        return 用户信息.get("状态") != "未注册"
    except Exception as e:
        错误日志器.error(f"检查用户注册状态失败: {str(e)}")
        return False


async def Postgre_获取用户含邀约上限(用户ID: int) -> Optional[Dict[str, Any]]:
    """
    获取用户信息包含邀约上限

    Args:
        用户ID: 用户ID

    Returns:
        包含邀约上限的用户信息
    """
    try:
        查询SQL = """
        SELECT id, 昵称, 手机号, 状态, 权限等级, 每日邀约次数,
               每日快递查询次数, 算力值, 可创建团队数, 是否自动审核
        FROM 用户表
        WHERE id = $1
        """

        async with Postgre_异步连接池实例.获取连接() as 连接:
            结果 = await 连接.fetchrow(查询SQL, 用户ID)

            if 结果:
                用户信息 = dict(结果)
                数据库日志器.debug(f"获取用户邀约上限信息成功: 用户ID={用户ID}")
                return 用户信息
            else:
                数据库日志器.debug(f"用户不存在: 用户ID={用户ID}")
                return None

    except Exception as e:
        错误日志器.error(f"获取用户邀约上限信息失败: {str(e)}")
        return None


async def Postgre_获取用户今日成功邀约次数(用户ID: int) -> int:
    """
    获取用户今日成功邀约次数

    Args:
        用户ID: 用户ID

    Returns:
        今日成功邀约次数
    """
    try:
        查询SQL = """
        SELECT COUNT(*) as 次数
        FROM 邀约日志表
        WHERE 邀约人ID = $1
        AND DATE(创建时间) = CURRENT_DATE
        AND 邀约状态 = '成功'
        """

        async with Postgre_异步连接池实例.获取连接() as 连接:
            结果 = await 连接.fetchrow(查询SQL, 用户ID)
            次数 = 结果['次数'] if 结果 else 0

            数据库日志器.debug(f"获取用户今日邀约次数成功: 用户ID={用户ID}, 次数={次数}")
            return 次数

    except Exception as e:
        错误日志器.error(f"获取用户今日邀约次数失败: {str(e)}")
        return 0


async def Postgre_获取用户昵称和邀约次数(用户ID: int) -> Optional[Dict[str, Any]]:
    """
    获取用户昵称和邀约次数信息

    Args:
        用户ID: 用户ID

    Returns:
        用户昵称和邀约次数信息
    """
    try:
        查询SQL = """
        SELECT id, 昵称, 每日邀约次数
        FROM 用户表
        WHERE id = $1
        """

        async with Postgre_异步连接池实例.获取连接() as 连接:
            结果 = await 连接.fetchrow(查询SQL, 用户ID)

            if 结果:
                用户信息 = dict(结果)
                数据库日志器.debug(f"获取用户昵称和邀约次数成功: 用户ID={用户ID}")
                return 用户信息
            else:
                数据库日志器.debug(f"用户不存在: 用户ID={用户ID}")
                return None

    except Exception as e:
        错误日志器.error(f"获取用户昵称和邀约次数失败: {str(e)}")
        return None


async def Postgre_更新用户昵称(用户ID: int, 新昵称: str) -> bool:
    """
    更新用户昵称

    Args:
        用户ID: 用户ID
        新昵称: 新的昵称

    Returns:
        更新是否成功
    """
    try:
        更新SQL = """
        UPDATE 用户表
        SET 昵称 = $2, updated_at = CURRENT_TIMESTAMP
        WHERE id = $1
        """

        async with Postgre_异步连接池实例.获取连接() as 连接:
            结果 = await 连接.execute(更新SQL, 用户ID, 新昵称)

            if 结果 == "UPDATE 1":
                数据库日志器.info(f"用户昵称更新成功: 用户ID={用户ID}, 新昵称={新昵称}")
                return True
            else:
                数据库日志器.warning(f"用户昵称更新失败，可能用户不存在: 用户ID={用户ID}")
                return False

    except Exception as e:
        错误日志器.error(f"更新用户昵称异常: {str(e)}")
        return False


async def Postgre_更新用户密码(用户ID: int, 新密码: str) -> bool:
    """
    更新用户密码

    Args:
        用户ID: 用户ID
        新密码: 新的密码

    Returns:
        更新是否成功
    """
    try:
        更新SQL = """
        UPDATE 用户表
        SET password = $2, updated_at = CURRENT_TIMESTAMP
        WHERE id = $1
        """

        async with Postgre_异步连接池实例.获取连接() as 连接:
            结果 = await 连接.execute(更新SQL, 用户ID, 新密码)
            影响行数 = int(结果.split()[-1]) if 结果.startswith('UPDATE') else 0

            if 影响行数 > 0:
                数据库日志器.info(f"用户密码更新成功: 用户ID={用户ID}")
                return True
            else:
                数据库日志器.warning(f"用户密码更新失败，可能用户不存在: 用户ID={用户ID}")
                return False

    except Exception as e:
        错误日志器.error(f"更新用户密码异常: {str(e)}")
        return False


async def Postgre_获取用户密码哈希(用户ID: int) -> Optional[str]:
    """
    获取用户密码哈希值

    Args:
        用户ID: 用户ID

    Returns:
        密码哈希值
    """
    try:
        查询SQL = """
        SELECT password
        FROM 用户表
        WHERE id = $1
        """

        async with Postgre_异步连接池实例.获取连接() as 连接:
            结果 = await 连接.fetchrow(查询SQL, 用户ID)

            if 结果:
                密码哈希 = 结果['password']
                数据库日志器.debug(f"获取用户密码哈希成功: 用户ID={用户ID}")
                return 密码哈希
            else:
                数据库日志器.debug(f"用户不存在: 用户ID={用户ID}")
                return None

    except Exception as e:
        错误日志器.error(f"获取用户密码哈希失败: {str(e)}")
        return None


async def Postgre_记录邀约日志(邀约数据: Dict[str, Any]) -> bool:
    """
    记录邀约日志

    Args:
        邀约数据: 邀约相关数据

    Returns:
        记录是否成功
    """
    try:
        插入SQL = """
        INSERT INTO 邀约日志表 (
            邀约人ID, 被邀约人手机号, 邀约状态, 状态备注,
            邀约时间, 创建时间
        ) VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP)
        """

        async with Postgre_异步连接池实例.获取连接() as 连接:
            await 连接.execute(
                插入SQL,
                邀约数据.get("邀约人ID"),
                邀约数据.get("被邀约人手机号"),
                邀约数据.get("邀约状态"),
                邀约数据.get("状态备注"),
                邀约数据.get("邀约时间", datetime.now())
            )

            数据库日志器.info(f"邀约日志记录成功: 邀约人ID={邀约数据.get('邀约人ID')}")
            return True

    except Exception as e:
        错误日志器.error(f"记录邀约日志异常: {str(e)}")
        return False


# ==================== 用户搜索功能 ====================

async def Postgre_搜索用户(
    关键词: str,
    分页参数: Dict[str, int],
    搜索字段: List[str] = None,
    状态过滤: str = None
) -> tuple[List[Dict[str, Any]], int]:
    """
    通用的用户搜索函数

    Args:
        关键词: 搜索关键词
        分页参数: 包含页码和每页数量的字典
        搜索字段: 要搜索的字段列表，默认为['昵称', '手机号', '邮箱']
        状态过滤: 状态过滤条件，如'未注册'、'已删除'等

    Returns:
        (用户列表, 总数量)
    """
    try:
        # 默认搜索字段
        if 搜索字段 is None:
            搜索字段 = ['昵称', '手机号', '邮箱']

        # 构建搜索条件
        搜索条件 = f"%{关键词}%"

        # 构建WHERE子句
        搜索条件列表 = []
        参数列表 = []
        参数索引 = 1

        # 添加搜索字段条件
        for 字段 in 搜索字段:
            搜索条件列表.append(f"COALESCE({字段}, '') LIKE ${参数索引}")
            参数列表.append(搜索条件)
            参数索引 += 1

        where_clause = f"WHERE ({' OR '.join(搜索条件列表)})"

        # 添加状态过滤
        if 状态过滤:
            where_clause += f" AND COALESCE(状态, 'active') != ${参数索引}"
            参数列表.append(状态过滤)
            参数索引 += 1

        # 查询总数
        计数SQL = f"""
        SELECT COUNT(*) as total
        FROM 用户表
        {where_clause}
        """

        # 分页参数
        页码 = 分页参数.get("页码", 1)
        每页数量 = 分页参数.get("每页数量", 20)
        偏移量 = (页码 - 1) * 每页数量

        # 查询用户列表
        用户SQL = f"""
        SELECT
            id, 昵称, 手机号, 邮箱, 状态, created_at,
            COALESCE(昵称, COALESCE(手机号, '用户' || id)) as 显示名称
        FROM 用户表
        {where_clause}
        ORDER BY created_at DESC
        LIMIT ${参数索引} OFFSET ${参数索引 + 1}
        """

        # 添加分页参数
        参数列表.extend([每页数量, 偏移量])

        async with Postgre_异步连接池实例.获取连接() as 连接:
            # 获取总数
            计数结果 = await 连接.fetchrow(计数SQL, *参数列表[:-2])  # 不包含分页参数
            总数量 = 计数结果["total"] if 计数结果 else 0

            # 获取用户列表
            用户列表 = await 连接.fetch(用户SQL, *参数列表)

            # 转换结果
            结果列表 = []
            for 用户 in 用户列表:
                用户信息 = dict(用户)
                # 处理datetime序列化
                if 用户信息.get("created_at"):
                    用户信息["created_at"] = 用户信息["created_at"].isoformat()
                结果列表.append(用户信息)

            数据库日志器.info(
                f"搜索用户成功，关键词: {关键词}，总数: {总数量}，返回: {len(结果列表)}"
            )
            return 结果列表, 总数量

    except Exception as e:
        错误日志器.error(f"搜索用户失败: {str(e)}")
        return [], 0
