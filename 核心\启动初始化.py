import os
import pathlib

from fastapi import FastAPI
from fastapi.routing import APIRoute

from 工具.限速器 import IP限速器实例  # 从迁移后的位置导入

# 假设这些全局实例和配置/日志函数可以在此模块中被导入

from 日志 import 初始化实时日志系统, 应用日志器, 记录系统启动, 错误日志器

# APP_NAME, APP_VERSION 将作为参数传入，BASE_DIR也作为参数传入


async def 初始化应用服务(
    app: FastAPI, base_dir: str, app_name: str, app_version: str
) -> None:
    """处理应用启动时的核心初始化逻辑"""
    应用日志器.info("🔧 正在初始化应用服务...")

    try:
        # 1. 确保必要目录存在
        必要目录列表 = [
            os.path.join(base_dir, "静态"),
            os.path.join(base_dir, "日志", "文件"),
            os.path.join(base_dir, "数据", "缓存"),
            os.path.join(base_dir, "数据", "临时文件"),
        ]
        for 目录 in 必要目录列表:
            pathlib.Path(目录).mkdir(parents=True, exist_ok=True)

        # 2. 记录系统启动信息
        记录系统启动(app_name, app_version)

        # 3. 初始化PostgreSQL数据库连接池
        from 数据.Postgre_异步连接池 import Postgre_异步连接池实例
        await Postgre_异步连接池实例.初始化数据库连接池()

        # 验证PostgreSQL数据库连接
        async with Postgre_异步连接池实例.获取连接() as 连接:
            结果 = await 连接.fetchval("SELECT 1")
            if 结果 != 1:
                raise Exception("PostgreSQL数据库连接测试失败")



        # 4. 启动IP限速器清理任务
        await IP限速器实例.启动清理任务()

        # 5. 初始化实时日志系统
        await 初始化实时日志系统()

        # 6. 同步内部函数工具到数据库
        try:
            from 数据.LangChain_工具数据层 import LangChain工具数据层实例
            await LangChain工具数据层实例.初始化()
            同步结果 = await LangChain工具数据层实例.同步内部函数工具到数据库()
            if 同步结果["success"]:
                应用日志器.info(f"🔧 内部函数工具同步完成: {同步结果['message']}")
            else:
                应用日志器.warning(f"⚠️ 内部函数工具同步失败: {同步结果['message']}")
        except Exception as e:
            应用日志器.warning(f"⚠️ 内部函数工具同步过程出现异常: {str(e)}")

        # 7. LangChain智能体系统采用懒加载机制，在API调用时自动初始化
        应用日志器.info(
            "ℹ️ LangChain智能体系统采用懒加载机制，将在首次API调用时自动初始化"
        )

        # 统计已注册的路由数量
        api_路由数 = len([route for route in app.routes if isinstance(route, APIRoute)])

        应用日志器.info(f"✅ 应用服务初始化完成 - API路由: {api_路由数} 个")

    except Exception as e:
        错误日志器.error(f"❌ 应用服务初始化失败: {e}")
        raise


async def 系统启动前检查():
    """系统启动前的环境检查"""
    try:
        应用日志器.info("开始系统启动前检查...")

        # 检查环境变量配置
        await 检查环境变量配置()

        # 检查数据库连接
        await 检查数据库连接()

        # 检查必要的依赖包
        await 检查依赖包()



        应用日志器.info("✅ 系统启动前检查完成")
        return True

    except Exception as e:
        应用日志器.error(f"系统启动前检查失败: {str(e)}")
        return False


async def 检查环境变量配置():
    """检查关键环境变量配置"""
    try:
        import os

        必需环境变量 = [
            "DB_HOST",
            "DB_USER",
            "DB_PASSWORD",
            "DB_NAME",
            "JWT_SECRET_KEY",
        ]

        可选环境变量 = [
            "OPENAI_API_KEY"  # 仅检查RAG功能需要的OpenAI密钥，其他密钥存储在数据库中
        ]

        # 检查必需变量
        缺失变量 = []
        for 变量名 in 必需环境变量:
            if not os.getenv(变量名):
                缺失变量.append(变量名)

        if 缺失变量:
            应用日志器.warning(f"缺失必需环境变量: {', '.join(缺失变量)}")
        else:
            应用日志器.info("✅ 必需环境变量检查通过")

        # 检查可选变量
        未配置可选变量 = []
        for 变量名 in 可选环境变量:
            if not os.getenv(变量名):
                未配置可选变量.append(变量名)

        if 未配置可选变量:
            应用日志器.info(
                f"未配置可选环境变量: {', '.join(未配置可选变量)}，相关功能将使用模拟模式"
            )

    except Exception as e:
        应用日志器.error(f"环境变量检查失败: {str(e)}")


async def 检查数据库连接():
    """检查数据库连接"""
    try:
        from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例

        # 测试数据库连接
        测试查询 = "SELECT 1 as test"
        结果 = await 异步连接池实例.执行查询(测试查询)

        if 结果:
            应用日志器.info("✅ 数据库连接检查通过")
        else:
            应用日志器.warning("⚠️ 数据库连接异常")

    except Exception as e:
        应用日志器.error(f"数据库连接检查失败: {str(e)}")


async def 检查依赖包():
    """检查关键依赖包"""
    try:
        依赖包检查结果 = []

        # 检查LangChain相关包
        try:
            import importlib.util

            if importlib.util.find_spec(
                "langchain_openai"
            ) and importlib.util.find_spec("langchain_community"):
                依赖包检查结果.append("✅ LangChain包可用")
            else:
                raise ImportError("LangChain包不完整")
        except ImportError as e:
            依赖包检查结果.append(f"⚠️ LangChain包缺失: {str(e)}")

        # 检查duckduckgo-search
        try:
            if importlib.util.find_spec("duckduckgo_search"):
                依赖包检查结果.append("✅ DuckDuckGo搜索包可用")
            else:
                raise ImportError("duckduckgo_search包不存在")
        except ImportError as e:
            依赖包检查结果.append(f"⚠️ DuckDuckGo搜索包缺失: {str(e)}")

        # 检查OpenAI
        try:
            if importlib.util.find_spec("openai"):
                依赖包检查结果.append("✅ OpenAI包可用")
            else:
                raise ImportError("openai包不存在")
        except ImportError as e:
            依赖包检查结果.append(f"⚠️ OpenAI包缺失: {str(e)}")

        for 结果 in 依赖包检查结果:
            应用日志器.info(结果)

    except Exception as e:
        应用日志器.error(f"依赖包检查失败: {str(e)}")



