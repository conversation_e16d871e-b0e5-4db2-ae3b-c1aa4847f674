"""
SuperAdmin 用户管理数据操作模块
负责处理用户CRUD操作、用户信息管理等相关的数据库操作
超级管理员专用 - 权限等级最高

功能包括：
- 用户列表查询（支持分页、筛选、排序）
- 用户增删改查操作
- 管理员权限验证
- 用户信息统计等高级用户管理功能
"""

from datetime import datetime
from typing import Optional, Dict, Any, List, Tuple

# PostgreSQL连接池导入
from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 日志 import 系统日志器, 错误日志器, 安全日志器


async def 异步获取用户列表(
    页码: int = 1, 
    每页数量: int = 10, 
    搜索关键词: Optional[str] = None, 
    邮箱筛选: Optional[str] = None, 
    手机号筛选: Optional[str] = None, 
    排序字段: Optional[str] = None, 
    排序顺序: Optional[str] = None
) -> Dict[str, Any]:
    """
    异步获取用户列表，支持分页、筛选、排序
    
    Args:
        页码: 页码，从1开始
        每页数量: 每页数量
        搜索关键词: 用户名筛选条件
        邮箱筛选: 邮箱筛选条件
        手机号筛选: 手机号筛选条件
        排序字段: 排序字段
        排序顺序: 排序顺序（asc/desc）
        
    Returns:
        Dict[str, Any]: 包含用户列表和分页信息的字典
    """
    try:
        偏移量 = (页码 - 1) * 每页数量
        查询参数 = []
        筛选条件 = []

        # {{ AURA-X: Modify - 添加IP地址信息，直接中文对接无映射. Approval: 寸止(ID:1721062800). }}
        # 优化的查询语句，包含用户状态和登录信息（从登录表获取最后登录时间和IP地址）
        基础查询 = """
        SELECT
            u.id,
            COALESCE(u.昵称, '未设置昵称') as 昵称,
            COALESCE(u.昵称, '未设置用户名') as 用户名,
            COALESCE(u.邮箱, '') as 邮箱,
            COALESCE(u.手机号, '') as 手机号,
            u.created_at,
            ul.登陆时间 as 最后登录时间,
            ul.IP地址 as 最后登录IP,
            ul.IP归属地 as 最后登录归属地,
            COALESCE(u.is_admin, 0) as is_admin,
            COALESCE(u.状态, 'active') as status,
            COALESCE(u.level, 1) as level,
            COALESCE(u.每日邀约次数, 30) as 每日邀约次数,
            CASE
                WHEN COALESCE(u.状态, 'active') = 'active' THEN '正常'
                WHEN u.状态 = 'inactive' THEN '未激活'
                WHEN u.状态 = 'banned' THEN '已禁用'
                ELSE '正常'
            END as 状态文本
        FROM 用户表 u
        LEFT JOIN (
            SELECT
                用户id,
                MAX(登陆时间) as 登陆时间,
                SUBSTRING_INDEX(GROUP_CONCAT(IP地址 ORDER BY 登陆时间 DESC), ',', 1) as IP地址,
                SUBSTRING_INDEX(GROUP_CONCAT(IP归属地 ORDER BY 登陆时间 DESC), ',', 1) as IP归属地
            FROM 用户登陆记录表
            GROUP BY 用户id
        ) ul ON u.id = ul.用户id
        """
        
        总数查询 = """SELECT COUNT(*) as total FROM 用户表 u
        LEFT JOIN (
            SELECT 用户id, MAX(登陆时间) as 登陆时间
            FROM 用户登陆记录表 
            GROUP BY 用户id
        ) ul ON u.id = ul.用户id"""

        # 构建筛选条件 - 智能搜索逻辑
        if 搜索关键词:
            # 智能判断搜索类型
            if 搜索关键词.isdigit():
                # 纯数字：根据长度判断是用户ID还是手机号
                if len(搜索关键词) <= 6:  # 用户ID通常较短
                    # 短数字：优先匹配用户ID，同时搜索手机号
                    筛选条件.append("(u.id = %s OR COALESCE(u.手机号, '') LIKE %s)")
                    查询参数.extend([int(搜索关键词), f"%{搜索关键词}%"])
                else:
                    # 长数字：主要搜索手机号，同时检查用户ID
                    筛选条件.append("(COALESCE(u.手机号, '') LIKE %s OR u.id = %s)")
                    查询参数.extend([f"%{搜索关键词}%", int(搜索关键词)])
            else:
                # 非纯数字：搜索昵称和邮箱
                筛选条件.append("(COALESCE(u.昵称, '') LIKE %s OR COALESCE(u.邮箱, '') LIKE %s)")
                查询参数.extend([f"%{搜索关键词}%", f"%{搜索关键词}%"])
        
        if 邮箱筛选:
            筛选条件.append("COALESCE(u.邮箱, '') LIKE %s")
            查询参数.append(f"%{邮箱筛选}%")

        if 手机号筛选:
            筛选条件.append("COALESCE(u.手机号, '') LIKE %s")
            查询参数.append(f"%{手机号筛选}%")
        
        # 组装WHERE条件
        条件语句 = ""
        if 筛选条件:
            条件语句 = " WHERE " + " AND ".join(筛选条件)
        
        # 构建排序语句 - 智能排序，精确匹配优先
        排序字段映射 = {
            "id": "u.id",
            "昵称": "COALESCE(u.昵称, '')",
            "created_at": "u.created_at",
            "邮箱": "COALESCE(u.邮箱, '')",
            "手机号": "COALESCE(u.手机号, '')",
            "最后登录时间": "ul.登陆时间"
        }

        # 智能排序：当搜索短数字时，精确匹配的用户ID优先显示
        if 搜索关键词 and 搜索关键词.isdigit() and len(搜索关键词) <= 6:
            # 短数字搜索：精确匹配用户ID的记录排在前面
            排序语句 = f"ORDER BY (u.id = {int(搜索关键词)}) DESC, u.id ASC"
        else:
            # 常规排序
            数据库排序字段 = 排序字段映射.get(排序字段 or "created_at", "u.created_at")
            # 处理前端传递的ascend/descend格式
            if 排序顺序 == "ascend":
                排序方向 = "ASC"
            elif 排序顺序 == "descend":
                排序方向 = "DESC"
            else:
                排序方向 = "DESC"  # 默认降序
            排序语句 = f"ORDER BY {数据库排序字段} {排序方向}"

        # 获取总记录数
        总数SQL = 总数查询 + 条件语句
        系统日志器.debug(f"执行总数查询: {总数SQL}, 参数: {查询参数}")
        总数结果 = await 异步连接池实例.执行查询(总数SQL, tuple(查询参数))
        总记录数 = 总数结果[0]["total"] if 总数结果 and 总数结果[0].get("total") is not None else 0
        
        # 获取用户列表数据
        列表SQL = 基础查询 + 条件语句 + f" {排序语句} LIMIT %s OFFSET %s"
        分页参数 = 查询参数 + [每页数量, 偏移量]
        系统日志器.debug(f"执行列表查询: {列表SQL}, 参数: {分页参数}")
        查询结果 = await 异步连接池实例.执行查询(列表SQL, tuple(分页参数))
        
        用户列表 = []
        if 查询结果:
            for 用户行 in 查询结果:
                try:
                    # 安全地转换为字典
                    if hasattr(用户行, 'keys'):
                        用户数据 = dict(用户行)
                    else:
                        用户数据 = dict(用户行)
                    
                    # 格式化日期时间，处理None值
                    for 日期字段 in ["created_at", "最后登录时间"]:
                        if 用户数据.get(日期字段):
                            if isinstance(用户数据[日期字段], datetime):
                                用户数据[日期字段] = 用户数据[日期字段].strftime("%Y-%m-%d %H:%M:%S")
                        else:
                            用户数据[日期字段] = None
                    
                    # {{ AURA-X: Modify - 简洁高效，直接使用中文字段，移除冗余兼容代码. Approval: 寸止(ID:1721062800). }}
                    # 确保必要字段存在且处理None值
                    用户数据["avatar"] = None  # 头像暂时为空
                    用户数据["status"] = 用户数据.get("status") or "active"
                    用户数据["is_admin"] = int(用户数据.get("is_admin", 0))

                    # 确保昵称和用户名不为空，统一使用中文字段
                    if not 用户数据.get("昵称") or 用户数据["昵称"] == "未设置昵称":
                        用户数据["昵称"] = 用户数据.get("手机号") or f"用户{用户数据['id']}"

                    if not 用户数据.get("用户名") or 用户数据["用户名"] == "未设置用户名":
                        用户数据["用户名"] = 用户数据.get("手机号") or f"用户{用户数据['id']}"
                    
                    用户列表.append(用户数据)
                except Exception as 行处理错误:
                    错误日志器.error(f"处理用户行数据失败: {行处理错误}, 行数据: {用户行}")
                    continue
        
        # 返回标准化的响应格式
        return {
            "列表": 用户列表,
            "总数": 总记录数,
            "页码": 页码,
            "每页数量": 每页数量,
            "总页数": (总记录数 + 每页数量 - 1) // 每页数量
        }
        
    except Exception as e:
        错误日志器.error(f"异步获取用户列表失败: {e}", exc_info=True)
        return {
            "列表": [],
            "总数": 0,
            "页码": 页码,
            "每页数量": 每页数量,
            "总页数": 0
        }


async def 异步获取用户关联店铺列表(用户ID: int, page: int = 1, limit: int = 10) -> Dict[str, Any]:
    """
    获取指定用户关联的店铺列表（分页）
    """
    try:
        offset = (page - 1) * limit
        
        # 查询总数
        count_sql = "SELECT COUNT(*) as total FROM `用户_店铺` WHERE 用户ID = %s"
        count_result = await 异步连接池实例.执行查询(count_sql, (用户ID,))
        total = count_result[0]['total'] if count_result else 0

        # 查询分页数据
        query_sql = """
            SELECT
                s.id as 店铺ID,
                s.shop_id as 店铺标识,
                s.shop_name as 店铺名称,
                s.avatar as 店铺头像,
                s.创建时间
            FROM `用户_店铺` us
            JOIN `店铺` s ON us.店铺ID = s.id
            WHERE us.用户ID = %s
            ORDER BY s.创建时间 DESC
            LIMIT %s OFFSET %s
        """
        shops = await 异步连接池实例.执行查询(query_sql, (用户ID, limit, offset))

        # 格式化店铺数据，使用中文字段名
        格式化店铺列表 = []
        for 店铺 in shops:
            格式化店铺列表.append({
                "店铺ID": 店铺['店铺ID'],
                "店铺标识": 店铺['店铺标识'] or '',
                "店铺名称": 店铺['店铺名称'] or '',
                "店铺头像": 店铺['店铺头像'] or '',
                "创建时间": 店铺['创建时间'].strftime("%Y-%m-%d %H:%M:%S") if 店铺['创建时间'] else '',
                "关联状态": "已关联"
            })

        return {
            "总数": total,
            "列表": 格式化店铺列表,
            "页码": page,
            "每页数量": limit
        }
    except Exception as e:
        错误日志器.error(f"异步获取用户关联店铺列表失败: 用户ID={用户ID}, 错误={e}", exc_info=True)
        return {"total": 0, "items": [], "page": page, "limit": limit}


async def 异步获取用户最后登录详情(用户ID: int) -> Optional[Dict[str, Any]]:
    """
    获取用户的最后一次登录记录详情
    """
    try:
        query_sql = """
            SELECT 登陆时间, IP地址, 登陆地点, UserAgent
            FROM `用户登陆记录表`
            WHERE 用户id = %s
            ORDER BY 登陆时间 DESC
            LIMIT 1
        """
        result = await 异步连接池实例.执行查询(query_sql, (用户ID,))
        
        if result and result[0]:
            return result[0]
        return None
    except Exception as e:
        错误日志器.error(f"异步获取用户最后登录详情失败: 用户ID={用户ID}, 错误={e}", exc_info=True)
        return None


async def 异步添加用户(
    用户名: str,
    邮箱: str,
    密码: str,
    手机号: Optional[str] = '',
    用户类型: str = '普通用户'
) -> Dict[str, Any]:
    """
    异步添加新用户
    
    Args:
        用户名: 用户名
        邮箱: 邮箱
        密码: 密码
        手机号: 手机号
        用户类型: 用户类型
        
    Returns:
        dict: 结果字典，包含success状态和消息或用户ID
    """
    try:
        # 检查用户名是否已存在
        结果 = await 异步连接池实例.执行查询("SELECT id FROM 用户表 WHERE 昵称 = %s", (用户名,))
        if 结果:
            return {'success': False, 'message': '用户名已存在'}
        
        # 检查邮箱是否已存在
        结果 = await 异步连接池实例.执行查询("SELECT id FROM 用户表 WHERE 邮箱 = %s", (邮箱,))
        if 结果:
            return {'success': False, 'message': '邮箱已存在'}
        
        # 如果提供了手机号，检查是否已存在
        if 手机号 and 手机号.strip():  # 确保手机号不为空
            结果 = await 异步连接池实例.执行查询("SELECT id FROM 用户表 WHERE 手机号 = %s", (手机号,))
            if 结果:
                return {'success': False, 'message': '手机号已存在'}
        else:
            手机号 = None  # 将空字符串转换为None，避免唯一索引冲突
        
        # 密码直接存储为明文
        
        # 插入新用户
        插入结果 = await 异步连接池实例.执行插入(
            """
            INSERT INTO 用户表
                (昵称, 邮箱, password, 手机号, created_at)
            VALUES
                (%s, %s, %s, %s, NOW())
            """,
            (用户名, 邮箱, 密码, 手机号)
        )
        
        系统日志器.info(f"成功添加用户: {用户名}, ID: {插入结果}")
        return {'success': True, 'userId': 插入结果}
            
    except Exception as e:
        错误日志器.error(f"异步添加用户失败: {e}", exc_info=True)
        # 处理特定的数据库错误
        if "Duplicate entry" in str(e):
            if "index_phone" in str(e):
                return {'success': False, 'message': '手机号已存在'}
            elif "email" in str(e):
                return {'success': False, 'message': '邮箱已存在'}
            elif "nickname" in str(e):
                return {'success': False, 'message': '用户名已存在'}
        return {'success': False, 'message': f'添加用户失败: {str(e)}'}


async def 异步更新用户(
    用户ID: int, 
    用户名: Optional[str] = None, 
    电话: Optional[str] = None, 
    邮箱: Optional[str] = None
) -> Dict[str, Any]:
    """
    异步更新用户信息
    
    Args:
        用户ID: 用户ID
        用户名: 可选，新用户名
        电话: 可选，新电话号码
        邮箱: 可选，新邮箱
        
    Returns:
        dict: 包含操作结果的字典
    """
    try:
        # 检查用户是否存在
        用户结果 = await 异步连接池实例.执行查询("SELECT id FROM 用户表 WHERE id = %s", (用户ID,))
        if not 用户结果:
            return {"成功": False, "消息": "用户不存在"}
        
        # 构建更新语句
        更新字段 = []
        参数 = []
        
        if 用户名:
            # 检查用户名是否已被其他用户使用
            结果 = await 异步连接池实例.执行查询(
                "SELECT id FROM 用户表 WHERE 昵称 = %s AND id != %s",
                (用户名, 用户ID)
            )
            if 结果:
                return {"成功": False, "消息": "用户名已被其他用户使用"}
            更新字段.append("昵称 = %s")
            参数.append(用户名)
        
        if 电话:
            # 检查电话是否已被其他用户使用
            结果 = await 异步连接池实例.执行查询(
                "SELECT id FROM 用户表 WHERE 手机号 = %s AND id != %s", 
                (电话, 用户ID)
            )
            if 结果:
                return {"成功": False, "消息": "电话号码已被其他用户使用"}
            更新字段.append("手机号 = %s")
            参数.append(电话)
            
        if 邮箱:
            # 检查邮箱是否已被其他用户使用
            结果 = await 异步连接池实例.执行查询(
                "SELECT id FROM 用户表 WHERE 邮箱 = %s AND id != %s", 
                (邮箱, 用户ID)
            )
            if 结果:
                return {"成功": False, "消息": "邮箱已被其他用户使用"}
            更新字段.append("邮箱 = %s")
            参数.append(邮箱)
        
        if not 更新字段:
            return {"成功": False, "消息": "没有提供要更新的字段"}
        
        # 执行更新
        更新语句 = f"UPDATE 用户表 SET {', '.join(更新字段)} WHERE id = %s"
        参数.append(用户ID)
        
        await 异步连接池实例.执行更新(更新语句, tuple(参数))
        
        系统日志器.info(f"成功更新用户信息: 用户ID {用户ID}")
        return {"成功": True, "消息": "用户信息更新成功"}
    except Exception as e:
        错误日志器.error(f"异步更新用户失败: {e}", exc_info=True)
        return {"成功": False, "消息": f"更新用户失败: {str(e)}"}


async def 异步获取用户信息(用户id: int) -> Optional[Dict[str, Any]]:
    """
    异步获取单个用户详细信息 - 优化版
    
    Args:
        用户id: 用户ID
        
    Returns:
        Optional[Dict[str, Any]]: 用户信息或None
    """
    try:
        用户结果 = await 异步连接池实例.执行查询(
            """
            SELECT
                id,
                COALESCE(昵称, '') as 昵称,
                COALESCE(邮箱, email, '') as 邮箱,
                COALESCE(手机号, phone, '') as 手机号,
                COALESCE(is_admin, 0) as is_admin,
                COALESCE(level, 1) as level,
                邀请人,
                created_at,
                COALESCE(状态, 'active') as 状态,
                COALESCE(每日邀约次数, 30) as 每日邀约次数
            FROM 用户表
            WHERE id = $1
            """,
            (用户id,)
        )
        
        if not 用户结果:
            系统日志器.warning(f"用户不存在: 用户ID={用户id}")
            return None
            
        用户 = 用户结果[0]
        
        # 处理日期格式
        创建时间 = ""
        最后登录时间 = ""

        if 用户.get("created_at"):
            if isinstance(用户["created_at"], datetime):
                创建时间 = 用户["created_at"].strftime('%Y-%m-%d %H:%M:%S')
            else:
                创建时间 = str(用户["created_at"])

        # 从登录记录表获取最后登录时间
        最后登录记录 = await 异步获取用户最后登录详情(用户id)
        if 最后登录记录 and 最后登录记录.get("登陆时间"):
            if isinstance(最后登录记录["登陆时间"], datetime):
                最后登录时间 = 最后登录记录["登陆时间"].strftime('%Y-%m-%d %H:%M:%S')
            else:
                最后登录时间 = str(最后登录记录["登陆时间"])
        
        # 返回统一的中文字段格式
        用户信息 = {
            "id": 用户["id"],
            "昵称": 用户.get("昵称", "") or f"用户{用户['id']}",
            "邮箱": 用户.get("邮箱", ""),
            "手机号": 用户.get("手机号", ""),
            "is_admin": bool(用户.get("is_admin", 0)),
            "level": 用户.get("level", 1),
            "状态": 用户.get("状态", "active"),
            "created_at": 创建时间,
            "last_login_time": 最后登录时间,
            "每日邀约次数": 用户.get("每日邀约次数", 30),
            "邀请人": 用户.get("邀请人"),
            "注册时间": 创建时间,
            "用户类型": "邀请用户" if 用户.get("邀请人") else "普通用户"
        }
        
        系统日志器.info(f"获取用户信息成功: 用户ID={用户id}, 昵称={用户信息.get('nickname')}")
        return 用户信息
    except Exception as e:
        错误日志器.error(f"异步获取用户信息失败: 用户ID={用户id}, 错误={e}", exc_info=True)
        return None


async def 异步删除用户(用户id: int) -> Dict[str, Any]:
    """
    异步删除用户 - 完全删除用户及其所有关联数据

    Args:
        用户id: 用户ID

    Returns:
        Dict[str, Any]: 操作结果
    """
    try:
        # 检查用户是否存在
        用户结果 = await 异步连接池实例.执行查询(
            "SELECT id, 昵称 FROM 用户表 WHERE id = %s", (用户id,)
        )
        if not 用户结果:
            return {'success': False, 'message': '用户不存在'}

        用户信息 = 用户结果[0]
        用户昵称 = 用户信息.get('昵称', f'用户{用户id}')

        系统日志器.info(f"开始删除用户及其关联数据: 用户ID={用户id}, 昵称={用户昵称}")

        # 根据外键约束规则处理用户删除
        # CASCADE: 会自动删除的表（由数据库外键约束处理）
        # SET NULL: 会自动设置为NULL的表（由数据库外键约束处理）
        # 手动处理: 没有外键约束的表需要手动删除

        删除统计 = {}

        # 1. 统计将被CASCADE删除的记录（这些会被数据库自动删除）
        CASCADE删除表列表 = [
            ("用户_店铺", "用户ID"),
            ("用户团队关联表", "用户ID"),
            ("用户团队权限表", "用户ID"),
            ("用户客户邀请记录表", "邀请人ID"),
            ("用户微信关联表", "用户ID"),
            ("用户抖音达人邀约记录表", "用户id"),
            ("用户权限表", "user_id")
        ]

        for 表名, 字段名 in CASCADE删除表列表:
            try:
                统计SQL = f"SELECT COUNT(*) as count FROM `{表名}` WHERE `{字段名}` = %s"
                统计结果 = await 异步连接池实例.执行查询(统计SQL, (用户id,))
                记录数 = 统计结果[0]['count'] if 统计结果 else 0
                删除统计[f"{表名}(CASCADE)"] = 记录数
                if 记录数 > 0:
                    系统日志器.info(f"{表名} 将通过CASCADE删除 {记录数} 条记录")
            except Exception as e:
                删除统计[f"{表名}(CASCADE)"] = f"统计失败: {str(e)}"

        # 2. 统计将被SET NULL的记录（这些会被数据库自动处理）
        SET_NULL表列表 = [
            ("团队操作日志表", "操作人ID"),
            ("团队表", "团队负责人ID"),
            ("用户团队关联表", "审批人ID"),
            ("用户团队关联表", "邀请人ID"),
            ("用户团队权限表", "授权人ID"),
            ("用户客户邀请记录表", "被邀请人ID")
        ]

        for 表名, 字段名 in SET_NULL表列表:
            try:
                统计SQL = f"SELECT COUNT(*) as count FROM `{表名}` WHERE `{字段名}` = %s"
                统计结果 = await 异步连接池实例.执行查询(统计SQL, (用户id,))
                记录数 = 统计结果[0]['count'] if 统计结果 else 0
                删除统计[f"{表名}(SET NULL)"] = 记录数
                if 记录数 > 0:
                    系统日志器.info(f"{表名} 将通过SET NULL处理 {记录数} 条记录")
            except Exception as e:
                删除统计[f"{表名}(SET NULL)"] = f"统计失败: {str(e)}"

        # 3. 手动删除没有外键约束的表
        手动删除表列表 = [
            ("用户登陆记录表", "用户id"),
            ("用户_会员_关联表", "用户id"),
            ("用户ai信息表", "用户ID")
        ]

        for 表名, 字段名 in 手动删除表列表:
            try:
                # 先查询要删除的记录数
                统计SQL = f"SELECT COUNT(*) as count FROM `{表名}` WHERE `{字段名}` = %s"
                统计结果 = await 异步连接池实例.执行查询(统计SQL, (用户id,))
                记录数 = 统计结果[0]['count'] if 统计结果 else 0

                if 记录数 > 0:
                    # 执行删除
                    删除SQL = f"DELETE FROM `{表名}` WHERE `{字段名}` = %s"
                    await 异步连接池实例.执行更新(删除SQL, (用户id,))
                    删除统计[f"{表名}(手动删除)"] = 记录数
                    系统日志器.info(f"手动删除 {表名} 中的 {记录数} 条记录")
                else:
                    删除统计[f"{表名}(手动删除)"] = 0

            except Exception as table_error:
                错误日志器.warning(f"手动删除表 {表名} 数据时出错: {table_error}")
                删除统计[f"{表名}(手动删除)"] = f"删除失败: {str(table_error)}"

        # 4. 最后删除用户表主记录（这会触发所有外键约束的CASCADE和SET NULL操作）
        try:
            删除用户SQL = "DELETE FROM 用户表 WHERE id = %s"
            await 异步连接池实例.执行更新(删除用户SQL, (用户id,))
            删除统计["用户表(主记录)"] = 1
            系统日志器.info(f"删除用户表主记录: 用户ID={用户id}")
        except Exception as e:
            错误日志器.error(f"删除用户表主记录失败: 用户ID={用户id}, 错误={e}")
            删除统计["用户表(主记录)"] = f"删除失败: {str(e)}"
            # 主记录删除失败，整个操作失败
            return {
                'success': False,
                'message': f'删除用户 "{用户昵称}" 失败: 主记录删除失败',
                'details': 删除统计
            }

        # 生成删除报告
        删除报告 = []
        for 表名, 结果 in 删除统计.items():
            if isinstance(结果, int):
                if 结果 > 0:
                    删除报告.append(f"{表名}: {结果}条记录")
            else:
                删除报告.append(f"{表名}: {结果}")

        系统日志器.info(f"用户删除完成: 用户ID={用户id}, 昵称={用户昵称}, 删除详情: {'; '.join(删除报告)}")

        return {
            'success': True,
            'message': f'用户 "{用户昵称}" 删除成功',
            'details': 删除统计
        }

    except Exception as e:
        错误日志器.error(f"异步删除用户失败: 用户ID={用户id}, 错误={e}", exc_info=True)
        return {'success': False, 'message': f'删除用户失败: {str(e)}'}


async def 异步验证管理员(用户名: str, 密码: str, ip地址: str = "127.0.0.1") -> Dict[str, Any]:
    """
    异步验证管理员用户名和密码，增强版
    
    Args:
        用户名: 用户名或邮箱或电话
        密码: 密码
        
    Returns:
        dict: 验证结果，包含成功标志，用户ID和消息
    """
    try:
        # 查询用户，使用昵称, 手机号或邮箱字段
        用户结果 = await 异步连接池实例.执行查询(
            """
            SELECT id, password, 邮箱, 手机号, 昵称, is_admin
            FROM 用户表
            WHERE 手机号 = %s OR 邮箱 = %s OR 昵称 = %s
            LIMIT 1
            """,
            (用户名, 用户名, 用户名)
        )
        
        if not 用户结果:
            # 记录失败的登录尝试
            安全日志器.warning(f"管理员登录失败: 用户 '{用户名}' 不存在")
            return {"成功": False, "消息": "用户名或密码错误"}
        
        用户 = 用户结果[0]
        存储密码 = 用户.get("password", "")
        
        # 密码验证 - 尝试直接比较密码
        密码正确 = False
        if 存储密码 == 密码:  # 明文密码比较
            密码正确 = True
        
        # 如果密码验证失败
        if not 密码正确:
            安全日志器.warning(f"管理员登录失败: 用户 '{用户名}' 密码错误")
            return {"成功": False, "消息": "用户名或密码错误"}
        
        # 检查是否为管理员
        是否管理员 = await 异步检查管理员权限(用户)

        if not 是否管理员:
            安全日志器.warning(f"管理员登录失败: 用户 '{用户名}' 不具备管理员权限")
            return {"成功": False, "消息": "该用户不是管理员"}

        # 记录成功登录到登录记录表，使用传入的真实IP地址
        await 记录管理员登录(用户["id"], ip地址)
        
        安全日志器.info(f"管理员登录成功: 用户ID {用户['id']}")
        return {
            "成功": True,
            "用户id": 用户["id"],
            "消息": "登录成功"
        }
    except Exception as e:
        错误日志器.error(f"异步验证管理员失败: {e}", exc_info=True)
        return {"成功": False, "消息": f"登录失败: {str(e)}"}


async def 异步检查管理员权限(用户: Dict[str, Any]) -> bool:
    """
    检查用户是否具有管理员权限
    
    Args:
        用户: 用户信息字典
        
    Returns:
        bool: 是否为管理员
    """
    用户ID = 用户.get("id")
    if 用户ID is None:
        安全日志器.warning(f"异步检查管理员权限：用户字典中缺少ID。用户数据: {用户}")
        return False

    try:
        安全日志器.info(f"开始检查用户ID {用户ID} 的管理员权限。")
        
        # 查询用户的 is_admin 状态
        用户权限结果 = await 异步连接池实例.执行查询(
            "SELECT is_admin FROM 用户表 WHERE id = %s",
            (用户ID,)
        )

        if 用户权限结果 and 用户权限结果[0].get("is_admin"): # 假设 is_admin 为 1 表示管理员
            安全日志器.info(f"用户ID {用户ID} 具有管理员权限 (is_admin=True)。")
            return True
        else:
            安全日志器.warning(f"用户ID {用户ID} 不具有管理员权限或is_admin未设置/为False。查询结果: {用户权限结果}")
            return False

    except Exception as e:
        # 这个最外层的except是为了捕捉 异步检查管理员权限 函数本身可能发生的、
        # 未被内部逻辑处理的意外错误（例如 获取用户ID 失败之外的其他逻辑错误）。
        安全日志器.error(f"异步检查管理员权限时发生错误，用户ID {用户ID}: {e}", exc_info=True)
        return False # 出现任何其他意外，安全起见返回False


async def 记录管理员登录(用户id: int, ip地址: str = "127.0.0.1") -> None:
    """
    记录管理员登录，包含IP归属地查询

    Args:
        用户id: 用户ID
        ip地址: 登录IP地址
    """
    try:
        # {{ AURA-X: Modify - 简单直接的IP归属地查询，不搞复杂. Approval: 寸止(ID:1721062800). }}
        from 工具.IP归属地查询 import 查询IP归属地

        # 查询IP归属地
        ip归属地 = 查询IP归属地(ip地址)

        await 异步连接池实例.执行插入(
            """
            INSERT INTO 用户登陆记录表 (用户id, 登陆时间, IP地址, IP归属地)
            VALUES (%s, NOW(), %s, %s)
            """,
            (用户id, ip地址, ip归属地)
        )
        系统日志器.info(f"记录管理员登录成功: 用户ID {用户id}, IP: {ip地址}, 归属地: {ip归属地}")
    except Exception as e:
        错误日志器.error(f"记录管理员登录失败: {e}", exc_info=True)
        # 登录记录失败不影响登录结果


# {{ AURA-X: Add - 添加用户注册统计函数，简洁高效直接对接. Approval: 寸止(ID:1721062800). }}
async def 异步获取用户注册统计(时间范围: str = "today") -> dict:
    """
    获取用户注册统计数据

    Args:
        时间范围: 时间范围 (today/week/month/lastMonth/recent30)

    Returns:
        dict: 用户注册统计数据
    """
    try:
        from datetime import datetime, timedelta
        import calendar

        现在 = datetime.now()
        时间轴 = []
        注册数据 = []

        if 时间范围 == "today":
            # 今天24小时，每小时统计
            开始时间 = datetime.combine(现在.date(), datetime.min.time())
            for i in range(24):
                小时开始 = 开始时间 + timedelta(hours=i)
                小时结束 = 小时开始 + timedelta(hours=1)
                时间轴.append(f"{i:02d}:00")

                # 查询该小时的注册数量
                查询SQL = """
                SELECT COUNT(*) as 注册数量
                FROM 用户表
                WHERE created_at >= $1 AND created_at < $2
                """
                结果 = await 异步连接池实例.执行查询(查询SQL, (小时开始, 小时结束))
                注册数据.append(结果[0]['注册数量'] if 结果 else 0)

        elif 时间范围 == "week":
            # 本周7天，每天统计
            今天 = 现在.date()
            本周开始 = 今天 - timedelta(days=今天.weekday())
            for i in range(7):
                日期 = 本周开始 + timedelta(days=i)
                时间轴.append(f"{日期.month}/{日期.day}")

                日期开始 = datetime.combine(日期, datetime.min.time())
                日期结束 = datetime.combine(日期, datetime.max.time())

                查询SQL = """
                SELECT COUNT(*) as 注册数量
                FROM 用户表
                WHERE created_at >= %s AND created_at <= %s
                """
                结果 = await 异步连接池实例.执行查询(查询SQL, (日期开始, 日期结束))
                注册数据.append(结果[0]['注册数量'] if 结果 else 0)

        elif 时间范围 == "month":
            # 本月每天统计
            今天 = 现在.date()
            本月第一天 = 今天.replace(day=1)
            本月天数 = calendar.monthrange(今天.year, 今天.month)[1]

            for day in range(1, 本月天数 + 1):
                日期 = 本月第一天.replace(day=day)
                时间轴.append(f"{day}日")

                日期开始 = datetime.combine(日期, datetime.min.time())
                日期结束 = datetime.combine(日期, datetime.max.time())

                查询SQL = """
                SELECT COUNT(*) as 注册数量
                FROM 用户表
                WHERE created_at >= %s AND created_at <= %s
                """
                结果 = await 异步连接池实例.执行查询(查询SQL, (日期开始, 日期结束))
                注册数据.append(结果[0]['注册数量'] if 结果 else 0)

        elif 时间范围 == "lastMonth":
            # 上月每天统计
            今天 = 现在.date()
            if 今天.month == 1:
                上月 = 今天.replace(year=今天.year-1, month=12, day=1)
            else:
                上月 = 今天.replace(month=今天.month-1, day=1)

            上月天数 = calendar.monthrange(上月.year, 上月.month)[1]

            for day in range(1, 上月天数 + 1):
                日期 = 上月.replace(day=day)
                时间轴.append(f"{day}日")

                日期开始 = datetime.combine(日期, datetime.min.time())
                日期结束 = datetime.combine(日期, datetime.max.time())

                查询SQL = """
                SELECT COUNT(*) as 注册数量
                FROM 用户表
                WHERE created_at >= %s AND created_at <= %s
                """
                结果 = await 异步连接池实例.执行查询(查询SQL, (日期开始, 日期结束))
                注册数据.append(结果[0]['注册数量'] if 结果 else 0)

        elif 时间范围 == "recent30":
            # 近30天，每天统计
            for i in range(29, -1, -1):
                日期 = (现在 - timedelta(days=i)).date()
                时间轴.append(f"{日期.month}/{日期.day}")

                日期开始 = datetime.combine(日期, datetime.min.time())
                日期结束 = datetime.combine(日期, datetime.max.time())

                查询SQL = """
                SELECT COUNT(*) as 注册数量
                FROM 用户表
                WHERE created_at >= %s AND created_at <= %s
                """
                结果 = await 异步连接池实例.执行查询(查询SQL, (日期开始, 日期结束))
                注册数据.append(结果[0]['注册数量'] if 结果 else 0)

        # 计算统计摘要
        总注册数 = sum(注册数据)
        日均注册 = 总注册数 / len(注册数据) if 注册数据 else 0
        最高单日 = max(注册数据) if 注册数据 else 0

        # 计算增长率（简化版本，与前一周期对比）
        增长率 = 0.0  # 暂时设为0，可以后续完善

        return {
            "时间轴": 时间轴,
            "注册数据": 注册数据,
            "总注册数": 总注册数,
            "日均注册": round(日均注册, 1),
            "最高单日": 最高单日,
            "增长率": 增长率
        }

    except Exception as e:
        错误日志器.error(f"获取用户注册统计失败: {e}", exc_info=True)
        return {
            "时间轴": [],
            "注册数据": [],
            "总注册数": 0,
            "日均注册": 0,
            "最高单日": 0,
            "增长率": 0.0
        }

async def 异步获取用户详细统计(用户ID: int) -> Dict[str, Any]:
    """
    获取用户的详细统计信息，包括关联店铺、登录记录、接口调用等
    """
    try:
        用户统计数据 = {}
        
        # 1. 获取用户基本信息
        基本信息SQL = """
        SELECT u.id, COALESCE(u.昵称, u.手机号, '') as 昵称,
               COALESCE(u.邮箱, '') as 邮箱, COALESCE(u.手机号, '') as 手机号,
               u.created_at as 注册时间,
               (SELECT MAX(登陆时间) FROM 用户登陆记录表 WHERE 用户id = u.id) as 上次登录时间,
               COALESCE(u.level, 1) as 等级, COALESCE(u.is_admin, 0) as 是否管理员,
               COALESCE(u.状态, 'active') as 状态
        FROM 用户表 u WHERE u.id = %s
        """
        基本信息结果 = await 异步连接池实例.执行查询(基本信息SQL, (用户ID,))
        用户统计数据["基本信息"] = 基本信息结果[0] if 基本信息结果 else {}
        
        # 2. 获取关联店铺数量和列表
        店铺SQL = """
        SELECT COUNT(*) as 店铺数量 FROM 用户_店铺 WHERE 用户ID = %s
        """
        店铺数量结果 = await 异步连接池实例.执行查询(店铺SQL, (用户ID,))
        
        店铺列表SQL = """
        SELECT s.id, s.shop_id, s.shop_name, s.avatar, s.创建时间
        FROM 用户_店铺 us
        JOIN 店铺 s ON us.店铺ID = s.id
        WHERE us.用户ID = %s
        ORDER BY s.创建时间 DESC
        LIMIT 5
        """
        店铺列表结果 = await 异步连接池实例.执行查询(店铺列表SQL, (用户ID,))
        
        用户统计数据["店铺信息"] = {
            "数量": 店铺数量结果[0]["店铺数量"] if 店铺数量结果 else 0,
            "列表": 店铺列表结果[:5] if 店铺列表结果 else []
        }
        
        # 3. 获取登录统计信息
        登录统计SQL = """
        SELECT 
            COUNT(*) as 总登录次数,
            MAX(登陆时间) as 最后登录时间,
            MIN(登陆时间) as 首次登录时间,
            COUNT(DISTINCT DATE(登陆时间)) as 活跃天数,
            COUNT(CASE WHEN 登陆时间 >= CURRENT_TIMESTAMP - INTERVAL '7 days' THEN 1 END) as 近7天登录次数,
            COUNT(CASE WHEN 登陆时间 >= CURRENT_TIMESTAMP - INTERVAL '30 days' THEN 1 END) as 近30天登录次数
        FROM 用户登陆记录表 WHERE 用户id = $1
        """
        登录统计结果 = await 异步连接池实例.执行查询(登录统计SQL, (用户ID,))
        
        # 获取最近的登录记录
        最近登录SQL = """
        SELECT 登陆时间, IP地址 FROM 用户登陆记录表 
        WHERE 用户id = $1
        ORDER BY 登陆时间 DESC 
        LIMIT 10
        """
        最近登录结果 = await 异步连接池实例.执行查询(最近登录SQL, (用户ID,))
        
        用户统计数据["登录信息"] = {
            "统计": 登录统计结果[0] if 登录统计结果 else {},
            "最近记录": 最近登录结果[:10] if 最近登录结果 else []
        }
        
        # 4. 获取接口调用统计
        接口调用统计SQL = """
        SELECT 
            COUNT(*) as 总调用次数,
            COUNT(DISTINCT DATE(创建时间)) as 使用天数,
            COUNT(CASE WHEN 创建时间 >= CURRENT_TIMESTAMP - INTERVAL '7 days' THEN 1 END) as 近7天调用次数,
            COUNT(CASE WHEN 创建时间 >= CURRENT_TIMESTAMP - INTERVAL '30 days' THEN 1 END) as 近30天调用次数,
            COUNT(CASE WHEN 状态码 >= 200 AND 状态码 < 300 THEN 1 END) as 成功次数,
            COUNT(CASE WHEN 状态码 >= 400 THEN 1 END) as 错误次数,
            AVG(耗时) as 平均耗时
        FROM 接口日志表 WHERE 用户ID = $1
        """
        接口统计结果 = await 异步连接池实例.执行查询(接口调用统计SQL, (用户ID,))
        
        # 获取接口调用频率统计
        接口频率SQL = """
        SELECT 请求路径, COUNT(*) as 调用次数
        FROM 接口日志表 
        WHERE 用户ID = $1 AND 创建时间 >= CURRENT_TIMESTAMP - INTERVAL '30 days'
        GROUP BY 请求路径
        ORDER BY 调用次数 DESC
        LIMIT 10
        """
        接口频率结果 = await 异步连接池实例.执行查询(接口频率SQL, (用户ID,))
        
        用户统计数据["接口调用"] = {
            "统计": 接口统计结果[0] if 接口统计结果 else {},
            "频率分析": 接口频率结果[:10] if 接口频率结果 else []
        }
        
        # 5. 获取AI使用统计（从AI对话记录获取）
        AI使用统计SQL = """
        SELECT
            '算力消耗模式' as 使用模式,
            COUNT(*) as 对话总数,
            COUNT(CASE WHEN DATE(创建时间) >= CURRENT_DATE - INTERVAL '30 days' THEN 1 END) as 近30天对话数,
            COUNT(CASE WHEN DATE(创建时间) >= CURRENT_DATE - INTERVAL '7 days' THEN 1 END) as 近7天对话数,
            MAX(创建时间) as 最后使用时间
        FROM AI对话记录表
        WHERE 用户ID = $1
        """
        AI使用结果 = await 异步连接池实例.执行查询(AI使用统计SQL, (用户ID,))

        用户统计数据["AI使用统计"] = AI使用结果[0] if AI使用结果 else {
            "使用模式": "算力消耗模式",
            "对话总数": 0,
            "近30天对话数": 0,
            "近7天对话数": 0,
            "最后使用时间": None
        }
        
        # 6. 获取团队关联信息
        团队信息SQL = """
        SELECT 
            t.团队名称, ut.职位, ut.状态, ut.加入时间,
            t.id as 团队ID, t.团队描述 as 团队描述
        FROM 用户团队关联表 ut
        JOIN 团队表 t ON ut.团队ID = t.id
        WHERE ut.用户ID = %s AND ut.状态 != '已移除'
        ORDER BY ut.加入时间 DESC
        """
        团队信息结果 = await 异步连接池实例.执行查询(团队信息SQL, (用户ID,))
        
        用户统计数据["团队信息"] = 团队信息结果 if 团队信息结果 else []
        
        # 7. 获取AI智能体信息
        AI信息SQL = """
        SELECT
            uai.员工名称, uai.店铺名称, uai.员工性格,
            uai.创建时间, uai.更新时间, uai.AI类型,
            am.名称 as 模型名称, am.算力消耗 as 算力消耗
        FROM 用户ai信息表 uai
        LEFT JOIN ai模型表 am ON uai.模型ID = am.id
        WHERE uai.用户ID = %s
        ORDER BY uai.创建时间 DESC
        """
        AI信息结果 = await 异步连接池实例.执行查询(AI信息SQL, (用户ID,))

        用户统计数据["AI信息"] = AI信息结果 if AI信息结果 else []
        
        return 用户统计数据
        
    except Exception as e:
        错误日志器.error(f"获取用户详细统计失败: 用户ID={用户ID}, 错误={e}", exc_info=True)
        return {}


async def 异步获取用户登录历史分页(用户ID: int, 页码: int = 1, 每页数量: int = 20) -> Dict[str, Any]:
    """
    分页获取用户登录历史记录
    """
    try:
        偏移量 = (页码 - 1) * 每页数量
        
        # 获取总数
        总数SQL = "SELECT COUNT(*) as total FROM 用户登陆记录表 WHERE 用户id = %s"
        总数结果 = await 异步连接池实例.执行查询(总数SQL, (用户ID,))
        总记录数 = 总数结果[0]["total"] if 总数结果 else 0
        
        # 获取分页数据
        登录记录SQL = """
        SELECT id, 登陆时间, IP地址, IP归属地,
               DATE_FORMAT(登陆时间, '%%Y-%%m-%%d %%H:%%i:%%s') as 格式化时间
        FROM 用户登陆记录表
        WHERE 用户id = %s
        ORDER BY 登陆时间 DESC
        LIMIT %s OFFSET %s
        """
        登录记录结果 = await 异步连接池实例.执行查询(登录记录SQL, (用户ID, 每页数量, 偏移量))
        
        return {
            "列表": 登录记录结果 if 登录记录结果 else [],
            "总数": 总记录数,
            "页码": 页码,
            "每页数量": 每页数量,
            "总页数": (总记录数 + 每页数量 - 1) // 每页数量
        }
        
    except Exception as e:
        错误日志器.error(f"获取用户登录历史失败: 用户ID={用户ID}, 错误={e}", exc_info=True)
        return {"列表": [], "总数": 0, "页码": 页码, "每页数量": 每页数量, "总页数": 0}


async def 异步获取用户接口调用历史分页(用户ID: int, 页码: int = 1, 每页数量: int = 20) -> Dict[str, Any]:
    """
    分页获取用户接口调用历史记录
    """
    try:
        偏移量 = (页码 - 1) * 每页数量
        
        # 获取总数
        总数SQL = "SELECT COUNT(*) as total FROM 接口日志表 WHERE 用户ID = %s"
        总数结果 = await 异步连接池实例.执行查询(总数SQL, (用户ID,))
        总记录数 = 总数结果[0]["total"] if 总数结果 else 0
        
        # 获取分页数据
        接口记录SQL = """
        SELECT id, 请求路径, 请求方法, 状态码, 耗时, IP地址,
               DATE_FORMAT(创建时间, '%%Y-%%m-%%d %%H:%%i:%%s') as 格式化时间,
               CASE 
                   WHEN 状态码 >= 200 AND 状态码 < 300 THEN '成功'
                   WHEN 状态码 >= 400 AND 状态码 < 500 THEN '客户端错误'
                   WHEN 状态码 >= 500 THEN '服务器错误'
                   ELSE '其他'
               END as 状态描述
        FROM 接口日志表 
        WHERE 用户ID = %s 
        ORDER BY 创建时间 DESC 
        LIMIT %s OFFSET %s
        """
        接口记录结果 = await 异步连接池实例.执行查询(接口记录SQL, (用户ID, 每页数量, 偏移量))
        
        return {
            "列表": 接口记录结果 if 接口记录结果 else [],
            "总数": 总记录数,
            "页码": 页码,
            "每页数量": 每页数量,
            "总页数": (总记录数 + 每页数量 - 1) // 每页数量
        }
        
    except Exception as e:
        错误日志器.error(f"获取用户接口调用历史失败: 用户ID={用户ID}, 错误={e}", exc_info=True)
        return {"列表": [], "总数": 0, "页码": 页码, "每页数量": 每页数量, "总页数": 0}


async def 异步获取用户邀约统计(用户ID: int) -> Dict[str, Any]:
    """
    获取用户邀约活动统计数据
    产品经理视角：了解用户的邀约行为和转化效果
    """
    try:
        # 邀约基础统计
        邀约统计SQL = """
        SELECT
            COUNT(*) as 总邀约次数,
            COUNT(DISTINCT DATE(邀约发起时间)) as 活跃邀约天数,
            COUNT(CASE WHEN 邀约状态 = '成功' THEN 1 END) as 成功邀约次数,
            COUNT(CASE WHEN 邀约状态 = '待处理' THEN 1 END) as 待处理邀约,
            COUNT(CASE WHEN 邀约状态 = '失败' THEN 1 END) as 失败邀约次数,
            COUNT(CASE WHEN 邀约发起时间 >= CURRENT_TIMESTAMP - INTERVAL '7 days' THEN 1 END) as 近7天邀约,
            COUNT(CASE WHEN 邀约发起时间 >= CURRENT_TIMESTAMP - INTERVAL '30 days' THEN 1 END) as 近30天邀约,
            MAX(邀约发起时间) as 最后邀约时间,
            AVG(当日计次) as 平均日计次
        FROM 用户抖音达人邀约记录表 WHERE 用户id = $1
        """
        邀约统计结果 = await 异步连接池实例.执行查询(邀约统计SQL, (用户ID,))
        
        # 获取邀约状态分布
        状态分布SQL = """
        SELECT 邀约状态, COUNT(*) as 数量,
               ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM 用户抖音达人邀约记录表 WHERE 用户id = $1), 2) as 百分比
        FROM 用户抖音达人邀约记录表
        WHERE 用户id = $2
        GROUP BY 邀约状态
        ORDER BY 数量 DESC
        """
        状态分布结果 = await 异步连接池实例.执行查询(状态分布SQL, (用户ID, 用户ID))
        
        # 获取近期邀约趋势
        趋势分析SQL = """
        SELECT DATE(邀约发起时间) as 日期, COUNT(*) as 邀约次数,
               COUNT(CASE WHEN 邀约状态 = '成功' THEN 1 END) as 成功次数
        FROM 用户抖音达人邀约记录表
        WHERE 用户id = $1 AND 邀约发起时间 >= CURRENT_TIMESTAMP - INTERVAL '30 days'
        GROUP BY DATE(邀约发起时间)
        ORDER BY 日期 DESC
        LIMIT 30
        """
        趋势分析结果 = await 异步连接池实例.执行查询(趋势分析SQL, (用户ID,))
        
        return {
            "基础统计": 邀约统计结果[0] if 邀约统计结果 else {},
            "状态分布": 状态分布结果 if 状态分布结果 else [],
            "趋势分析": 趋势分析结果 if 趋势分析结果 else []
        }
        
    except Exception as e:
        错误日志器.error(f"获取用户邀约统计失败: 用户ID={用户ID}, 错误={e}", exc_info=True)
        return {}


async def 异步获取用户权限详情(用户ID: int) -> Dict[str, Any]:
    """
    获取用户权限详细信息
    产品经理视角：全面了解用户的权限状态和使用情况
    """
    try:
        # 获取用户会员权限信息 - 基于会员关联表的新架构
        权限详情SQL = """
        SELECT
            m.id as 会员id,
            m.名称 as 会员名称,
            um.开通时间,
            um.到期时间,
            CASE
                WHEN STR_TO_DATE(um.到期时间, '%%Y-%%m-%%d %%H:%%i:%%s') > NOW() THEN '有效'
                ELSE '已过期'
            END as 会员状态,
            DATEDIFF(STR_TO_DATE(um.到期时间, '%%Y-%%m-%%d %%H:%%i:%%s'), NOW()) as 剩余天数,
            GROUP_CONCAT(p.名称 SEPARATOR ', ') as 权限列表
        FROM 用户_会员_关联表 um
        LEFT JOIN 会员表 m ON um.会员id = m.id
        LEFT JOIN 会员_权限_关联表 mp ON m.id = mp.会员id
        LEFT JOIN 权限表 p ON mp.权限id = p.id
        WHERE um.用户id = %s
        GROUP BY m.id, m.名称, um.开通时间, um.到期时间
        ORDER BY um.开通时间 DESC
        """
        权限详情结果 = await 异步连接池实例.执行查询(权限详情SQL, (用户ID,))
        
        # 获取AI智能体使用情况 - 优先查询用户ai信息表
        AI使用SQL = """
        SELECT
            uai.员工名称, uai.店铺名称, uai.员工性格,
            uai.创建时间, uai.更新时间, uai.AI类型,
            am.名称 as 模型名称, am.算力消耗 as 算力消耗,
            am.描述 as 模型描述,
            '正常使用' as 状态,
            0 as 剩余天数
        FROM 用户ai信息表 uai
        LEFT JOIN ai模型表 am ON uai.模型ID = am.id
        WHERE uai.用户ID = %s
        ORDER BY uai.创建时间 DESC
        """
        AI使用结果 = await 异步连接池实例.执行查询(AI使用SQL, (用户ID,))



        # 格式化权限列表数据，匹配前端表格列
        格式化权限列表 = []
        for 权限记录 in (权限详情结果 if 权限详情结果 else []):
            格式化权限列表.append({
                "权限名称": 权限记录.get('权限列表', ''),
                "权限状态": 权限记录.get('会员状态', ''),
                "开通时间": 权限记录.get('开通时间', ''),
                "到期时间": 权限记录.get('到期时间', ''),
                "剩余天数": 权限记录.get('剩余天数', 0)
            })

        # 格式化套餐信息数据
        格式化套餐信息 = []
        for 权限记录 in (权限详情结果 if 权限详情结果 else []):
            格式化套餐信息.append({
                "会员名称": 权限记录.get('会员名称', ''),
                "会员状态": 权限记录.get('会员状态', ''),
                "开通时间": 权限记录.get('开通时间', ''),
                "到期时间": 权限记录.get('到期时间', ''),
                "剩余天数": 权限记录.get('剩余天数', 0),
                "包含权限": 权限记录.get('权限列表', '')
            })

        return {
            "权限列表": 格式化权限列表,
            "套餐信息": 格式化套餐信息,
            "AI信息": AI使用结果 if AI使用结果 else []
        }
        
    except Exception as e:
        错误日志器.error(f"获取用户权限详情失败: 用户ID={用户ID}, 错误={e}", exc_info=True)
        return {}


async def 异步获取用户安全审计(用户ID: int) -> Dict[str, Any]:
    """
    获取用户安全审计信息
    产品经理视角：监控用户的安全行为和风险指标
    """
    try:
        # 登录安全分析
        登录安全SQL = """
        SELECT 
            COUNT(DISTINCT IP地址) as 不同IP数量,
            COUNT(*) as 总登录次数,
            COUNT(CASE WHEN 登陆时间 >= CURRENT_TIMESTAMP - INTERVAL '1 day' THEN 1 END) as 今日登录,
            COUNT(CASE WHEN 登陆时间 >= CURRENT_TIMESTAMP - INTERVAL '7 days' THEN 1 END) as 近7天登录,
            MAX(登陆时间) as 最后登录时间,
            MIN(登陆时间) as 首次登录时间
        FROM 用户登陆记录表 WHERE 用户id = $1
        """
        登录安全结果 = await 异步连接池实例.执行查询(登录安全SQL, (用户ID,))
        
        # 获取常用IP地址
        常用IP_SQL = """
        SELECT IP地址, COUNT(*) as 登录次数,
               MAX(登陆时间) as 最后使用时间,
               MIN(登陆时间) as 首次使用时间
        FROM 用户登陆记录表 
        WHERE 用户id = $1
        GROUP BY IP地址
        ORDER BY 登录次数 DESC
        LIMIT 10
        """
        常用IP结果 = await 异步连接池实例.执行查询(常用IP_SQL, (用户ID,))
        
        # 异常登录检测
        异常登录SQL = """
        SELECT 登陆时间, IP地址,
               LAG(IP地址) OVER (ORDER BY 登陆时间) as 上次IP,
               LAG(登陆时间) OVER (ORDER BY 登陆时间) as 上次时间
        FROM 用户登陆记录表 
        WHERE 用户id = $1
        ORDER BY 登陆时间 DESC
        LIMIT 20
        """
        异常登录结果 = await 异步连接池实例.执行查询(异常登录SQL, (用户ID,))
        
        # 接口调用安全
        接口安全SQL = """
        SELECT 
            COUNT(CASE WHEN 状态码 >= 400 AND 状态码 < 500 THEN 1 END) as 客户端错误次数,
            COUNT(CASE WHEN 状态码 >= 500 THEN 1 END) as 服务器错误次数,
            COUNT(CASE WHEN 耗时 > 5000 THEN 1 END) as 超时请求次数,
            COUNT(DISTINCT IP地址) as 接口调用IP数,
            MAX(创建时间) as 最后接口调用时间,
            COUNT(CASE WHEN 创建时间 >= CURRENT_TIMESTAMP - INTERVAL '1 hour' THEN 1 END) as 近1小时调用次数
        FROM 接口日志表 WHERE 用户ID = %s
        """
        接口安全结果 = await 异步连接池实例.执行查询(接口安全SQL, (用户ID,))
        
        return {
            "登录安全": 登录安全结果[0] if 登录安全结果 else {},
            "常用IP": 常用IP结果 if 常用IP结果 else [],
            "近期登录": 异常登录结果 if 异常登录结果 else [],
            "接口安全": 接口安全结果[0] if 接口安全结果 else {}
        }
        
    except Exception as e:
        错误日志器.error(f"获取用户安全审计失败: 用户ID={用户ID}, 错误={e}", exc_info=True)
        return {}


async def 异步更新用户状态(用户ID: int, 新状态: str, 操作员ID: int, 备注: str = "") -> Dict[str, Any]:
    """
    更新用户状态
    产品经理视角：提供用户状态管理功能（激活、禁用、冻结等）
    """
    try:
        # 获取当前用户状态
        当前状态SQL = "SELECT 状态, 昵称 FROM 用户表 WHERE id = %s"
        当前状态结果 = await 异步连接池实例.执行查询(当前状态SQL, (用户ID,))
        
        if not 当前状态结果:
            return {"status": 404, "message": "用户不存在"}
        
        原状态 = 当前状态结果[0]["状态"]
        用户昵称 = 当前状态结果[0]["昵称"]
        
        # 更新用户状态
        更新状态SQL = "UPDATE 用户表 SET 状态 = %s WHERE id = %s"
        await 异步连接池实例.执行更新(更新状态SQL, (新状态, 用户ID))
        
        # 记录状态变更日志
        日志SQL = """
        INSERT INTO 团队操作日志表 (团队ID, 操作人ID, 操作类型, 操作内容, 操作对象ID, 操作前数据, 操作后数据, IP地址)
        VALUES (0, %s, '用户状态变更', %s, %s, %s, %s, '127.0.0.1')
        """
        操作内容 = f"将用户 {用户昵称}(ID:{用户ID}) 状态从 '{原状态}' 变更为 '{新状态}'"
        if 备注:
            操作内容 += f"，备注：{备注}"
            
        import json
        操作前数据 = json.dumps({"状态": 原状态}, ensure_ascii=False)
        操作后数据 = json.dumps({"状态": 新状态, "备注": 备注}, ensure_ascii=False)
        
        await 异步连接池实例.执行更新(日志SQL, (
            操作员ID, 操作内容, 用户ID, 操作前数据, 操作后数据
        ))
        
        系统日志器.info(f"用户状态更新成功: 用户ID={用户ID}, 原状态={原状态}, 新状态={新状态}, 操作员={操作员ID}")
        return {"status": 100, "message": "用户状态更新成功"}
        
    except Exception as e:
        错误日志器.error(f"更新用户状态失败: 用户ID={用户ID}, 错误={e}", exc_info=True)
        return {"status": 500, "message": f"状态更新失败: {str(e)}"}


async def 异步批量操作用户(用户ID列表: List[int], 操作类型: str, 操作员ID: int, 参数: Optional[Dict] = None) -> Dict[str, Any]:
    """
    批量操作用户
    产品经理视角：提供批量用户管理功能
    """
    try:
        成功数量 = 0
        失败列表 = []

        # 确保参数不为None
        if 参数 is None:
            参数 = {}

        for 用户ID in 用户ID列表:
            try:
                if 操作类型 == "状态变更":
                    新状态 = 参数.get("状态", "正常")
                    备注 = 参数.get("备注", "批量操作")
                    结果 = await 异步更新用户状态(用户ID, 新状态, 操作员ID, 备注)
                    if 结果.get("status") == 100:
                        成功数量 += 1
                    else:
                        失败列表.append({"用户ID": 用户ID, "错误": 结果.get("message")})
                        
                elif 操作类型 == "重置密码":
                    # 这里可以添加重置密码逻辑
                    成功数量 += 1
                    
                elif 操作类型 == "发送通知":
                    # 这里可以添加发送通知逻辑
                    成功数量 += 1
                    
            except Exception as e:
                失败列表.append({"用户ID": 用户ID, "错误": str(e)})
        
        return {
            "status": 100,
            "message": f"批量操作完成，成功: {成功数量}, 失败: {len(失败列表)}",
            "data": {
                "成功数量": 成功数量,
                "失败数量": len(失败列表),
                "失败详情": 失败列表
            }
        }
        
    except Exception as e:
        错误日志器.error(f"批量操作用户失败: 错误={e}", exc_info=True)
        return {"status": 500, "message": f"批量操作失败: {str(e)}"}


async def 异步获取用户数据导出(导出类型: str = "基础信息", 筛选条件: Optional[Dict] = None) -> Dict[str, Any]:
    """
    导出用户数据
    产品经理视角：提供数据导出功能用于分析和报告
    """
    try:
        if 导出类型 == "基础信息":
            导出SQL = """
            SELECT 
                id as 用户ID,
                昵称,
                nickname as 英文昵称,
                手机号,
                邮箱,
                level as 等级,
                experience_points as 经验值,
                created_at as 注册时间,
                上次登录时间,
                每日邀约次数,
                状态,
                CASE WHEN is_admin = 1 THEN '是' ELSE '否' END as 是否管理员
            FROM 用户表
            """
            
        elif 导出类型 == "详细统计":
            导出SQL = """
            SELECT 
                u.id as 用户ID,
                u.昵称,
                u.phone as 手机号,
                u.email as 邮箱,
                u.状态,
                u.created_at as 注册时间,
                COUNT(DISTINCT us.店铺ID) as 关联店铺数,
                COUNT(DISTINCT ul.id) as 登录次数,
                MAX(ul.登陆时间) as 最后登录时间,
                COUNT(DISTINCT uy.id) as 邀约次数,
                COUNT(CASE WHEN uy.邀约状态 = '成功' THEN 1 END) as 成功邀约次数
            FROM 用户表 u
            LEFT JOIN 用户_店铺 us ON u.id = us.用户ID
            LEFT JOIN 用户登陆记录表 ul ON u.id = ul.用户id
            LEFT JOIN 用户抖音达人邀约记录表 uy ON u.id = uy.用户id
            GROUP BY u.id
            """
            
        else:
            return {"status": 400, "message": "不支持的导出类型"}
        
        # 添加筛选条件
        if 筛选条件:
            where_conditions = []
            params = []
            
            if 筛选条件.get("状态"):
                where_conditions.append("u.状态 = %s")
                params.append(筛选条件["状态"])
                
            if 筛选条件.get("注册时间范围"):
                开始时间, 结束时间 = 筛选条件["注册时间范围"]
                where_conditions.append("u.created_at BETWEEN %s AND %s")
                params.extend([开始时间, 结束时间])
            
            if where_conditions:
                if "WHERE" not in 导出SQL:
                    导出SQL += " WHERE " + " AND ".join(where_conditions)
                else:
                    导出SQL += " AND " + " AND ".join(where_conditions)
        else:
            params = []
        
        导出SQL += " ORDER BY u.id DESC LIMIT 10000"  # 限制导出数量防止内存溢出
        
        导出结果 = await 异步连接池实例.执行查询(导出SQL, tuple(params))
        
        return {
            "status": 100,
            "message": "数据导出成功",
            "data": {
                "导出类型": 导出类型,
                "记录数量": len(导出结果) if 导出结果 else 0,
                "数据": 导出结果 if 导出结果 else []
            }
        }
        
    except Exception as e:
        错误日志器.error(f"用户数据导出失败: 导出类型={导出类型}, 错误={e}", exc_info=True)
        return {"status": 500, "message": f"数据导出失败: {str(e)}"}


async def 异步获取用户行为分析(用户ID: Optional[int] = None, 分析维度: str = "活跃度") -> Dict[str, Any]:
    """
    用户行为分析
    产品经理视角：深度分析用户行为模式，为产品优化提供数据支持
    """
    try:
        if 分析维度 == "活跃度":
            # 用户活跃度分析
            活跃度SQL = """
            SELECT 
                DATE(登陆时间) as 日期,
                COUNT(DISTINCT 用户id) as 活跃用户数,
                COUNT(*) as 登录次数
            FROM 用户登陆记录表 
            WHERE 登陆时间 >= CURRENT_TIMESTAMP - INTERVAL '30 days'
            GROUP BY DATE(登陆时间)
            ORDER BY 日期 DESC
            """
            活跃度结果 = await 异步连接池实例.执行查询(活跃度SQL, ())
            
            return {"活跃度趋势": 活跃度结果}
            
        elif 分析维度 == "留存率":
            # 用户留存率分析
            留存SQL = """
            SELECT
                DATE(u.created_at) as 注册日期,
                COUNT(*) as 注册用户数,
                COUNT(CASE WHEN ul.登陆时间 >= u.created_at + INTERVAL '1 day'
                      AND ul.登陆时间 < u.created_at + INTERVAL '2 days' THEN 1 END) as 次日留存,
                COUNT(CASE WHEN ul.登陆时间 >= u.created_at + INTERVAL '7 days'
                      AND ul.登陆时间 < u.created_at + INTERVAL '8 days' THEN 1 END) as 七日留存
            FROM 用户表 u
            LEFT JOIN 用户登陆记录表 ul ON u.id = ul.用户id
            WHERE u.created_at >= CURRENT_TIMESTAMP - INTERVAL '30 days'
            GROUP BY DATE(u.created_at)
            ORDER BY 注册日期 DESC
            """
            留存结果 = await 异步连接池实例.执行查询(留存SQL, ())
            
            return {"留存率分析": 留存结果}
            
        elif 分析维度 == "功能使用":
            # 功能使用情况分析
            功能使用SQL = """
            SELECT 
                请求路径,
                COUNT(*) as 使用次数,
                COUNT(DISTINCT 用户ID) as 使用用户数,
                AVG(耗时) as 平均耗时,
                COUNT(CASE WHEN 状态码 >= 200 AND 状态码 < 300 THEN 1 END) / COUNT(*) * 100 as 成功率
            FROM 接口日志表 
            WHERE 创建时间 >= CURRENT_TIMESTAMP - INTERVAL '7 days'
            GROUP BY 请求路径
            ORDER BY 使用次数 DESC
            LIMIT 20
            """
            功能使用结果 = await 异步连接池实例.执行查询(功能使用SQL, ())
            
            return {"功能使用排行": 功能使用结果}
            
        return {"status": 400, "message": "不支持的分析维度"}
        
    except Exception as e:
        错误日志器.error(f"用户行为分析失败: 分析维度={分析维度}, 错误={e}", exc_info=True)
        return {"status": 500, "message": f"行为分析失败: {str(e)}"} 